<template>
  <div class="modal fade" id="notificationsModal" tabindex="-1" aria-labelledby="notificationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="notificationsModalLabel">
            <v-icon class="me-2">mdi-bell</v-icon>
            Todas as Notificações
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        
        <div class="modal-body p-0">
          <!-- Filtros -->
          <div class="notifications-filters p-3 border-bottom">
            <div class="row g-3">
              <div class="col-md-4">
                <label class="form-label small">Período</label>
                <select v-model="selectedPeriod" @change="applyFilters" class="form-select form-select-sm">
                  <option value="7">Últimos 7 dias</option>
                  <option value="30">Últimos 30 dias</option>
                  <option value="90">Últimos 90 dias</option>
                  <option value="all">Todas</option>
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label small">Status</label>
                <select v-model="selectedStatus" @change="applyFilters" class="form-select form-select-sm">
                  <option value="all">Todas</option>
                  <option value="unread">Não lidas</option>
                  <option value="read">Lidas</option>
                </select>
              </div>
              <div class="col-md-4">
                <label class="form-label small">Tipo</label>
                <select v-model="selectedType" @change="applyFilters" class="form-select form-select-sm">
                  <option value="all">Todos</option>
                  <option value="info">Informação</option>
                  <option value="success">Sucesso</option>
                  <option value="warning">Aviso</option>
                  <option value="error">Erro</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Loading -->
          <div v-if="isLoading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-2 text-muted">Carregando notificações...</p>
          </div>

          <!-- Lista de notificações -->
          <div v-else-if="filteredNotifications.length > 0" class="notifications-list" style="max-height: 400px; overflow-y: auto;">
            <div 
              v-for="notification in filteredNotifications" 
              :key="notification.id"
              class="notification-item-modal"
              :class="{ 'unread': !notification.read }"
            >
              <div class="d-flex align-items-start p-3 border-bottom">
                <div class="notification-icon me-3">
                  <v-icon 
                    :color="getNotificationColor(notification.type)"
                    size="24"
                  >
                    {{ getNotificationIcon(notification.type) }}
                  </v-icon>
                </div>
                
                <div class="notification-content flex-grow-1">
                  <div class="d-flex justify-content-between align-items-start mb-1">
                    <h6 class="notification-title mb-1">{{ notification.title }}</h6>
                    <small class="text-muted">{{ formatDate(notification.created_at) }}</small>
                  </div>
                  <p class="notification-message mb-2">{{ notification.message }}</p>
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="badge" :class="getTypeBadgeClass(notification.type)">
                      {{ getTypeLabel(notification.type) }}
                    </span>
                    <div class="notification-actions">
                      <button 
                        @click="toggleReadStatus(notification)"
                        class="btn btn-sm btn-outline-secondary me-2"
                        :title="notification.read ? 'Marcar como não lida' : 'Marcar como lida'"
                      >
                        <v-icon size="16">
                          {{ notification.read ? 'mdi-email-open' : 'mdi-email' }}
                        </v-icon>
                      </button>
                      <button 
                        @click="deleteNotification(notification.id)"
                        class="btn btn-sm btn-outline-danger"
                        title="Excluir notificação"
                      >
                        <v-icon size="16">mdi-delete</v-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sem notificações -->
          <div v-else class="text-center py-5">
            <v-icon size="64" color="grey-lighten-2" class="mb-3">mdi-bell-outline</v-icon>
            <h6 class="text-muted">Nenhuma notificação encontrada</h6>
            <p class="text-muted small">Tente ajustar os filtros acima</p>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="d-flex justify-content-between w-100">
            <div>
              <button 
                v-if="hasUnreadInFiltered"
                @click="markAllAsRead"
                class="btn btn-outline-primary"
                :disabled="isLoading"
              >
                <v-icon size="16" class="me-1">mdi-email-open</v-icon>
                Marcar todas como lidas
              </button>
            </div>
            <div>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                Fechar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import notificationsService from '@/services/notificationsService'

export default {
  name: 'NotificationsModal',
  
  data() {
    return {
      selectedPeriod: '30',
      selectedStatus: 'all',
      selectedType: 'all',
      allNotifications: [],
      isLoading: false
    }
  },
  
  computed: {
    ...mapGetters('notifications', [
      'hasUnreadNotifications'
    ]),
    
    filteredNotifications() {
      let filtered = [...this.allNotifications]
      
      // Filtrar por período
      if (this.selectedPeriod !== 'all') {
        const days = parseInt(this.selectedPeriod)
        const cutoffDate = new Date()
        cutoffDate.setDate(cutoffDate.getDate() - days)
        
        filtered = filtered.filter(notification => {
          const notificationDate = new Date(notification.created_at)
          return notificationDate >= cutoffDate
        })
      }
      
      // Filtrar por status
      if (this.selectedStatus === 'read') {
        filtered = filtered.filter(n => n.read)
      } else if (this.selectedStatus === 'unread') {
        filtered = filtered.filter(n => !n.read)
      }
      
      // Filtrar por tipo
      if (this.selectedType !== 'all') {
        filtered = filtered.filter(n => n.type === this.selectedType)
      }
      
      // Ordenar por data (mais recentes primeiro)
      return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    },
    
    hasUnreadInFiltered() {
      return this.filteredNotifications.some(n => !n.read)
    }
  },
  
  methods: {
    async loadAllNotifications() {
      this.isLoading = true
      try {
        // Buscar mais notificações (últimos 90 dias)
        const response = await notificationsService.fetchNotifications({ 
          perPage: 100,
          isRefresh: true 
        })
        this.allNotifications = response.data || []
      } catch (error) {
        console.error('Erro ao carregar todas as notificações:', error)
        this.allNotifications = []
      } finally {
        this.isLoading = false
      }
    },
    
    applyFilters() {
      // Os filtros são aplicados automaticamente via computed property
    },
    
    async toggleReadStatus(notification) {
      try {
        if (notification.read) {
          await notificationsService.markAsUnread(notification.id)
        } else {
          await notificationsService.markAsRead(notification.id)
        }
        
        // Atualizar a notificação local
        const index = this.allNotifications.findIndex(n => n.id === notification.id)
        if (index !== -1) {
          this.allNotifications[index].read = !this.allNotifications[index].read
          this.allNotifications[index].read_at = notification.read ? null : new Date().toISOString()
        }
      } catch (error) {
        console.error('Erro ao alterar status de leitura:', error)
      }
    },
    
    async deleteNotification(notificationId) {
      if (confirm('Tem certeza que deseja excluir esta notificação?')) {
        try {
          await notificationsService.deleteNotification(notificationId)
          
          // Remover da lista local
          const index = this.allNotifications.findIndex(n => n.id === notificationId)
          if (index !== -1) {
            this.allNotifications.splice(index, 1)
          }
        } catch (error) {
          console.error('Erro ao excluir notificação:', error)
        }
      }
    },
    
    async markAllAsRead() {
      try {
        await notificationsService.markAllAsRead()
        
        // Atualizar notificações locais
        this.allNotifications.forEach(notification => {
          if (!notification.read) {
            notification.read = true
            notification.read_at = new Date().toISOString()
          }
        })
      } catch (error) {
        console.error('Erro ao marcar todas como lidas:', error)
      }
    },
    
    formatDate(dateString) {
      return notificationsService.formatNotificationDate(dateString)
    },
    
    getNotificationIcon(type) {
      return notificationsService.getNotificationIcon(type)
    },
    
    getNotificationColor(type) {
      return notificationsService.getNotificationColor(type)
    },
    
    getTypeLabel(type) {
      const labels = {
        info: 'Informação',
        success: 'Sucesso',
        warning: 'Aviso',
        error: 'Erro'
      }
      return labels[type] || 'Informação'
    },
    
    getTypeBadgeClass(type) {
      const classes = {
        info: 'bg-info',
        success: 'bg-success',
        warning: 'bg-warning text-dark',
        error: 'bg-danger'
      }
      return classes[type] || 'bg-info'
    }
  },
  
  async mounted() {
    // Carregar notificações quando o modal for montado
    await this.loadAllNotifications()
  }
}
</script>

<style scoped>
.notifications-filters {
  background-color: #f8f9fa;
}

.notifications-list {
  border: none;
}

.notification-item-modal {
  transition: background-color 0.2s ease;
}

.notification-item-modal:hover {
  background-color: #f8f9fa;
}

.notification-item-modal.unread {
  background-color: #f0f8ff;
  border-left: 4px solid #007bff;
}

.notification-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0;
}

.notification-message {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 0;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 123, 255, 0.1);
}

.notification-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item-modal:hover .notification-actions {
  opacity: 1;
}

/* Scrollbar customizada */
.notifications-list::-webkit-scrollbar {
  width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notifications-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsivo */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 10px;
  }

  .notifications-list {
    max-height: 300px !important;
  }

  .notification-actions {
    opacity: 1; /* Sempre visível em mobile */
  }
}
</style>
