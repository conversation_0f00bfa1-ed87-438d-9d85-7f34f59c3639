<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="calendar-hover-card"
      :style="cardStyle"
      :class="{ 'past-time': isPastTime }"
    >
      <div class="card-content">
        <div class="card-icon">
          <font-awesome-icon 
            :icon="isPastTime ? ['fas', 'clock'] : ['fas', 'plus']" 
            :class="isPastTime ? 'text-warning' : 'text-primary'"
          />
        </div>
        <div class="card-text">
          <div class="card-title">
            {{ isPastTime ? 'Horário passado' : 'Agendar consulta' }}
          </div>
          <div class="card-subtitle">
            {{ formattedDateTime }}
          </div>
        </div>
      </div>
      <div class="card-arrow"></div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface Props {
  visible: boolean;
  x: number;
  y: number;
  date: Date | null;
  time?: string | null;
  view: 'day' | 'week' | 'month';
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  x: 0,
  y: 0,
  date: null,
  time: null,
  view: 'day'
});

// Posição do card
const cardStyle = computed(() => {
  const offset = 15; // Distância do cursor
  return {
    left: `${props.x + offset}px`,
    top: `${props.y - offset}px`,
    transform: 'translateY(-100%)'
  };
});

// Verificar se é horário passado
const isPastTime = computed(() => {
  if (!props.date) return false;
  
  const now = new Date();
  const targetDate = new Date(props.date);
  
  if (props.time && props.view !== 'month') {
    // Para visualizações com horário específico
    const [hours, minutes] = props.time.split(':').map(Number);
    targetDate.setHours(hours, minutes, 0, 0);
    return targetDate < now;
  } else {
    // Para visualização mensal (apenas data)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    targetDate.setHours(0, 0, 0, 0);
    return targetDate < today;
  }
});

// Formatação da data/hora
const formattedDateTime = computed(() => {
  if (!props.date) return '';
  
  const date = new Date(props.date);
  const dateStr = date.toLocaleDateString('pt-BR', {
    weekday: 'short',
    day: '2-digit',
    month: 'short'
  });
  
  if (props.time && props.view !== 'month') {
    return `${dateStr} às ${props.time}`;
  } else {
    return dateStr;
  }
});
</script>

<style lang="scss" scoped>
.calendar-hover-card {
  position: fixed;
  z-index: 9999;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 12px 16px;
  pointer-events: none;
  opacity: 0;
  animation: fadeInCard 0.2s ease forwards;
  backdrop-filter: blur(8px);
  min-width: 200px;
  
  &.past-time {
    background: rgba(249, 115, 22, 0.05);
    border-color: rgba(249, 115, 22, 0.2);
  }
  
  .card-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: rgba(20, 112, 233, 0.1);
    
    .past-time & {
      background: rgba(249, 115, 22, 0.1);
    }
    
    svg {
      font-size: 14px;
    }
  }
  
  .card-text {
    flex: 1;
  }
  
  .card-title {
    font-size: 13px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
    
    .past-time & {
      color: #f97316;
    }
  }
  
  .card-subtitle {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }
  
  .card-arrow {
    position: absolute;
    bottom: -6px;
    left: 20px;
    width: 12px;
    height: 12px;
    background: white;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    transform: rotate(45deg);
    
    .past-time & {
      background: rgba(249, 115, 22, 0.05);
      border-color: rgba(249, 115, 22, 0.2);
    }
  }
}

@keyframes fadeInCard {
  from {
    opacity: 0;
    transform: translateY(-100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(-100%) scale(1);
  }
}

.text-primary {
  color: #1470e9 !important;
}

.text-warning {
  color: #f97316 !important;
}
</style>
