// Importação dinâmica da store para evitar importação circular
let store = null
const getStore = () => {
  if (!store) {
    store = require('@/store').default
  }
  return store
}

class NotificationsService {
  constructor() {
    this.pollingInterval = null
    this.isPolling = false
    this.isInitialized = false
  }

  /**
   * Inicializar o serviço de notificações
   */
  async init() {
    if (this.isInitialized) {
      console.log('Serviço de notificações já foi inicializado')
      return true
    }

    try {
      // Buscar contagem inicial de notificações não lidas
      await this.fetchUnreadCount()

      // Iniciar polling automático
      this.startPolling()

      this.isInitialized = true
      console.log('Serviço de notificações inicializado com sucesso')
      return true
    } catch (error) {
      console.error('Erro ao inicializar serviço de notificações:', error)
      return false
    }
  }

  /**
   * Buscar todas as notificações
   */
  async fetchNotifications(options = {}) {
    try {
      return await getStore().dispatch('notifications/fetchNotifications', options)
    } catch (error) {
      console.error('Erro ao buscar notificações:', error)
      throw error
    }
  }

  /**
   * Atualizar notificações (refresh sem limpar lista)
   */
  async refreshNotifications(options = {}) {
    try {
      return await getStore().dispatch('notifications/fetchNotifications', { ...options, isRefresh: true })
    } catch (error) {
      console.error('Erro ao atualizar notificações:', error)
      throw error
    }
  }

  /**
   * Buscar contagem de notificações não lidas
   * @param {Object} options - Opções para a busca
   * @param {boolean} options.silent - Se true, não propaga erros (usado no polling)
   */
  async fetchUnreadCount(options = {}) {
    try {
      return await getStore().dispatch('notifications/fetchUnreadCount', options)
    } catch (error) {
      // Se não for modo silencioso, propagar o erro
      if (!options.silent) {
        console.error('Erro ao buscar contagem de não lidas:', error)
        throw error
      }
      // Em modo silencioso, apenas retornar null
      return null
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(notificationId) {
    try {
      return await getStore().dispatch('notifications/markAsRead', notificationId)
    } catch (error) {
      console.error('Erro ao marcar como lida:', error)
      throw error
    }
  }

  /**
   * Marcar notificação como não lida
   */
  async markAsUnread(notificationId) {
    try {
      return await getStore().dispatch('notifications/markAsUnread', notificationId)
    } catch (error) {
      console.error('Erro ao marcar como não lida:', error)
      throw error
    }
  }

  /**
   * Marcar todas as notificações como lidas
   */
  async markAllAsRead() {
    try {
      return await getStore().dispatch('notifications/markAllAsRead')
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error)
      throw error
    }
  }

  /**
   * Deletar notificação
   */
  async deleteNotification(notificationId) {
    try {
      return await getStore().dispatch('notifications/deleteNotification', notificationId)
    } catch (error) {
      console.error('Erro ao deletar notificação:', error)
      throw error
    }
  }

  /**
   * Criar nova notificação
   */
  async createNotification(notificationData) {
    try {
      return await getStore().dispatch('notifications/createNotification', notificationData)
    } catch (error) {
      console.error('Erro ao criar notificação:', error)
      throw error
    }
  }

  /**
   * Iniciar polling automático para verificar novas notificações
   */
  startPolling(interval = 30000) {
    if (this.isPolling) {
      this.stopPolling()
    }

    this.isPolling = true
    getStore().dispatch('notifications/startPolling', interval)

    // Também configurar polling adaptativo baseado na atividade do usuário
    this.setupAdaptivePolling(interval)

    console.log('Polling de notificações iniciado')
  }

  /**
   * Configurar polling adaptativo baseado na atividade do usuário
   */
  setupAdaptivePolling(baseInterval) {
    let currentInterval = baseInterval
    let lastActivity = Date.now()
    let inactivityTimer = null

    // Detectar atividade do usuário
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']

    const updateActivity = () => {
      lastActivity = Date.now()

      // Se estava em modo inativo, voltar ao polling normal
      if (currentInterval > baseInterval) {
        currentInterval = baseInterval
        this.restartPolling(currentInterval)
      }
    }

    // Adicionar listeners de atividade
    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, true)
    })

    // Verificar inatividade periodicamente
    const checkInactivity = () => {
      const timeSinceActivity = Date.now() - lastActivity

      // Se inativo por mais de 2 minutos, reduzir frequência
      if (timeSinceActivity > 120000 && currentInterval === baseInterval) {
        currentInterval = baseInterval * 3 // 3x menos frequente
        this.restartPolling(currentInterval)
        console.log('Usuário inativo - reduzindo frequência do polling')
      }

      // Se inativo por mais de 10 minutos, reduzir ainda mais
      if (timeSinceActivity > 600000 && currentInterval < baseInterval * 6) {
        currentInterval = baseInterval * 6 // 6x menos frequente
        this.restartPolling(currentInterval)
        console.log('Usuário muito inativo - reduzindo mais a frequência do polling')
      }
    }

    // Verificar inatividade a cada minuto
    inactivityTimer = setInterval(checkInactivity, 60000)

    // Armazenar referências para cleanup
    this.activityListeners = activityEvents.map(event => ({
      event,
      handler: updateActivity
    }))
    this.inactivityTimer = inactivityTimer
  }

  /**
   * Reiniciar polling com novo intervalo
   */
  restartPolling(newInterval) {
    getStore().dispatch('notifications/stopPolling')
    getStore().dispatch('notifications/startPolling', newInterval)
  }

  /**
   * Parar polling automático
   */
  stopPolling() {
    if (this.isPolling) {
      this.isPolling = false
      getStore().dispatch('notifications/stopPolling')

      // Limpar listeners de atividade
      if (this.activityListeners) {
        this.activityListeners.forEach(({ event, handler }) => {
          document.removeEventListener(event, handler, true)
        })
        this.activityListeners = null
      }

      // Limpar timer de inatividade
      if (this.inactivityTimer) {
        clearInterval(this.inactivityTimer)
        this.inactivityTimer = null
      }

      console.log('Polling de notificações parado')
    }
  }

  /**
   * Verificar se há notificações não lidas
   */
  hasUnreadNotifications() {
    return getStore().getters['notifications/hasUnreadNotifications']
  }

  /**
   * Obter contagem de notificações não lidas
   */
  getUnreadCount() {
    return getStore().getters['notifications/unreadCount']
  }

  /**
   * Obter todas as notificações
   */
  getAllNotifications() {
    return getStore().getters['notifications/allNotifications']
  }

  /**
   * Obter apenas notificações não lidas
   */
  getUnreadNotifications() {
    return getStore().getters['notifications/unreadNotifications']
  }

  /**
   * Obter notificação por ID
   */
  getNotificationById(id) {
    return getStore().getters['notifications/getNotificationById'](id)
  }

  /**
   * Obter notificações por tipo
   */
  getNotificationsByType(type) {
    return getStore().getters['notifications/getNotificationsByType'](type)
  }

  /**
   * Verificar se está carregando
   */
  isLoading() {
    return getStore().getters['notifications/isLoading']
  }

  /**
   * Verificar se está atualizando (refresh)
   */
  isRefreshing() {
    return getStore().getters['notifications/isRefreshing']
  }

  /**
   * Obter erro atual
   */
  getError() {
    return getStore().getters['notifications/error']
  }

  /**
   * Limpar erro
   */
  clearError() {
    getStore().dispatch('notifications/clearError')
  }

  /**
   * Destruir o serviço (cleanup)
   */
  destroy() {
    this.stopPolling()
    this.isInitialized = false
  }

  /**
   * Mostrar notificação toast (para notificações em tempo real)
   */
  showToast(notification) {
    // Implementar toast notification aqui
    // Pode usar uma biblioteca como vue-toastification
    console.log('Nova notificação:', notification)
  }

  /**
   * Formatar data da notificação
   */
  formatNotificationDate(dateString) {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))

    if (diffInMinutes < 1) {
      return 'Agora'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m atrás`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}h atrás`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}d atrás`
    }
  }

  /**
   * Obter ícone baseado no tipo da notificação
   */
  getNotificationIcon(type) {
    const icons = {
      info: 'mdi-information',
      success: 'mdi-check-circle',
      warning: 'mdi-alert',
      error: 'mdi-alert-circle'
    }
    return icons[type] || icons.info
  }

  /**
   * Obter cor baseada no tipo da notificação
   */
  getNotificationColor(type) {
    const colors = {
      info: 'blue',
      success: 'green',
      warning: 'orange',
      error: 'red'
    }
    return colors[type] || colors.info
  }
}

// Exportar instância singleton
export default new NotificationsService()
