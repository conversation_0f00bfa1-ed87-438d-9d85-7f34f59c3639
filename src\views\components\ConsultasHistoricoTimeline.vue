<template>
  <div class="consultas-historico-container">
    <!-- Container centralizado para o conteúdo -->
    <div class="timeline-wrapper">
      <!-- Dropdown customizado para registrar histórico/consulta -->
      <div class="header-actions mb-4">
        <div class="dropdown-custom" ref="dropdownContainer" @mouseenter="abrirDropdown" @mouseleave="fecharDropdown">
          <button
            class="btn btn-outline-primary btn-registrar-historico"
            type="button"
            :class="{ 'dropdown-aberto': dropdownAberto }"
            title="Registrar nova consulta ou histórico"
          >
            <div class="btn-content">
              <font-awesome-icon :icon="['fas', 'chevron-down']" class="dropdown-icon" :class="{ 'rotated': dropdownAberto }" />
              <span class="btn-text">Registrar consulta/histórico</span>
            </div>
          </button>
          <div class="dropdown-menu-custom" :class="{ 'show': dropdown<PERSON><PERSON> }">
            <div class="dropdown-item-custom" @click="selecionarOpcao('historico')">
              <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-2" />
              Histórico Simples
            </div>
            <div class="dropdown-item-custom" @click="selecionarOpcao('consulta')">
              <font-awesome-icon :icon="['fas', 'user-md']" class="me-2" />
              Consulta Realizada
            </div>
          </div>
        </div>
      </div>

      <div v-if="isLoading" class="w-100 text-center py-3">
        <div class="spinner-border text-primary" role="status"></div>
        <p class="mt-2">Carregando dados do paciente...</p>
      </div>

      <div v-else-if="!itensTimeline.length" class="empty-state">
        <div class="empty-state-message">
          <div class="icon-wrapper">
            <font-awesome-icon :icon="['fas', 'calendar-times']" class="empty-state-icon" />
          </div>
          <p>Não há consultas ou histórico registrados para este paciente.</p>
          <p class="text-muted small">Clique em "Registrar Histórico" para adicionar o primeiro registro.</p>
        </div>
      </div>

      <div v-else class="timeline-container">
        <!-- Item futuro: O que fazer na próxima consulta -->
        <div v-if="proximaConsultaInfo && !ultimaConsultaSemProximaConsulta" class="timeline-item futuro-item">
          <div class="timeline-badge futuro-badge">
            <font-awesome-icon :icon="['fas', 'arrow-right']" />
          </div>
          <div class="timeline-panel futuro-panel">
            <div class="timeline-heading">
              <div class="d-flex justify-content-between align-items-center">
                <h6 class="timeline-title">
                  O que fazer na próxima consulta
                  <span class="futuro-indicator">Próximo</span>
                </h6>
              </div>
              <p class="timeline-date">
                <small>Baseado na última consulta realizada</small>
              </p>
            </div>
            <div class="timeline-body">
              <p>{{ proximaConsultaInfo }}</p>
            </div>
          </div>
        </div>

        <!-- Itens temporários em edição -->
        <div v-for="(item, index) in itensTemporarios" :key="'temp-' + index" class="timeline-item">
          <!-- Timestamp do item temporário -->
          <div class="timeline-timestamp">
            <div class="timestamp-content">
              <div class="timestamp-date">{{ formatarDataTimestamp(item.data) }}</div>
              <div class="timestamp-time">{{ item.horario }}h</div>
            </div>
          </div>

          <!-- Badge do item temporário -->
          <div class="timeline-badge" :class="[getTimelineBadgeClass(item), getTimelineBadgeColorClass(item)]">
            <font-awesome-icon :icon="['fas', getTimelineIcon(item)]" />
          </div>

          <!-- Panel do item temporário com estilo igual aos existentes -->
          <div class="timeline-panel" :class="getTimelinePanelClass(item)">
            <!-- Consulta temporária -->
            <div v-if="item.tipo === 'consulta'" class="consulta-compacta">
              <div class="consulta-header">
                <div class="consulta-info">
                  <div class="consulta-categoria">
                    <div class="header-row">
                      <div class="title-section">
                        <h6 class="consulta-title mb-0">
                          <font-awesome-icon :icon="['fas', 'user-md']" class="me-2" />
                          Novo Registro de Consulta
                          <span class="badge bg-warning ms-2 badge-small">Em edição</span>
                        </h6>
                      </div>
                      <div class="actions-section">
                        <button
                          @click="salvarItemTemporario(item, index)"
                          class="btn btn-primary btn-sm me-2"
                          :disabled="!isItemTemporarioValido(item)"
                        >
                          <font-awesome-icon :icon="['fas', 'save']" class="me-1" />
                          Salvar
                        </button>
                        <button
                          @click="cancelarItemTemporario(index)"
                          class="btn btn-outline-secondary btn-sm"
                        >
                          <font-awesome-icon :icon="['fas', 'times']" class="me-1" />
                          Cancelar
                        </button>
                      </div>
                    </div>
                    <div class="categoria-selector mt-2">
                      <label class="categoria-label">Categoria:</label>
                      <select v-model="item.categoria" class="form-select form-select-sm categoria-select ms-2">
                        <option value="">Selecione categoria...</option>
                        <option v-for="cat in categorias" :key="cat.valor" :value="cat.valor">
                          {{ cat.nome }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Campos editáveis da consulta -->
              <div class="consulta-detalhes mt-3">
                <!-- Data e Horário -->
                <div class="row g-2 mb-3">
                  <div class="col-md-4">
                    <label class="field-label">
                      <font-awesome-icon :icon="['fas', 'calendar']" class="label-icon" />
                      Data
                    </label>
                    <input type="date" v-model="item.data" class="form-control form-control-sm field-input" />
                  </div>
                  <div class="col-md-4">
                    <label class="field-label">
                      <font-awesome-icon :icon="['fas', 'clock']" class="label-icon" />
                      Horário
                    </label>
                    <input type="time" v-model="item.horario" class="form-control form-control-sm field-input" />
                  </div>
                  <div class="col-md-4">
                    <label class="field-label">
                      <font-awesome-icon :icon="['fas', 'dollar-sign']" class="label-icon" />
                      Valor
                    </label>
                    <input type="number" step="0.01" v-model="item.valor" class="form-control form-control-sm field-input" placeholder="0,00" />
                  </div>
                </div>

                <!-- Histórico e Próxima Consulta lado a lado -->
                <div class="historico-fields">
                  <div class="row g-3">
                    <div class="col-md-6">
                      <div class="historico-item editing">
                        <div class="historico-header">
                          <h6 class="historico-title">
                            <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-2" />
                            Histórico da consulta
                          </h6>
                        </div>
                        <div class="historico-content">
                          <textarea
                            v-model="item.historico_consulta"
                            class="form-control historico-textarea"
                            rows="4"
                            placeholder="Descreva o que foi realizado durante esta consulta..."
                          ></textarea>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="historico-item editing">
                        <div class="historico-header">
                          <h6 class="historico-title">
                            <font-awesome-icon :icon="['fas', 'arrow-right']" class="me-2" />
                            O que fazer na próxima consulta
                          </h6>
                        </div>
                        <div class="historico-content">
                          <textarea
                            v-model="item.proxima_consulta"
                            class="form-control proxima-textarea"
                            rows="4"
                            placeholder="Descreva o que deve ser feito na próxima consulta..."
                          ></textarea>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Histórico temporário -->
            <div v-else class="historico-simples">
              <div class="historico-header mb-3">
                <div class="header-row">
                  <div class="title-section">
                    <h6 class="historico-title mb-0">
                      <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-2" />
                      Novo Registro de Histórico
                      <span class="badge bg-warning ms-2 badge-small">Em edição</span>
                    </h6>
                  </div>
                  <div class="actions-section">
                    <button
                      @click="salvarItemTemporario(item, index)"
                      class="btn btn-primary btn-sm me-2"
                      :disabled="!isItemTemporarioValido(item)"
                    >
                      <font-awesome-icon :icon="['fas', 'save']" class="me-1" />
                      Salvar
                    </button>
                    <button
                      @click="cancelarItemTemporario(index)"
                      class="btn btn-outline-secondary btn-sm"
                    >
                      <font-awesome-icon :icon="['fas', 'times']" class="me-1" />
                      Cancelar
                    </button>
                  </div>
                </div>
              </div>

              <!-- Campos editáveis do histórico -->
              <div class="row g-2 mb-3">
                <div class="col-md-6">
                  <label class="field-label">
                    <font-awesome-icon :icon="['fas', 'calendar']" class="label-icon" />
                    Data
                  </label>
                  <input type="date" v-model="item.data" class="form-control form-control-sm field-input" />
                </div>
                <div class="col-md-6">
                  <label class="field-label">
                    <font-awesome-icon :icon="['fas', 'clock']" class="label-icon" />
                    Horário
                  </label>
                  <input type="time" v-model="item.horario" class="form-control form-control-sm field-input" />
                </div>
              </div>

              <div class="historico-item editing">
                <div class="historico-content">
                  <label class="field-label">
                    <font-awesome-icon :icon="['fas', 'edit']" class="label-icon" />
                    Descrição do Histórico
                  </label>
                  <textarea
                    v-model="item.descricao"
                    class="form-control historico-textarea"
                    rows="4"
                    placeholder="Descreva o que aconteceu nesta data, observações importantes..."
                  ></textarea>
                </div>
              </div>
            </div>


          </div>
        </div>

        <!-- Timeline items normais -->
        <div v-for="(item, index) in itensTimeline" :key="index" class="timeline-item">
          <!-- Timestamp destacada na timeline -->
          <div class="timeline-timestamp"
               :class="{ 'clickable-timestamp': item.tipo === 'consulta' }"
               @click="item.tipo === 'consulta' ? editarConsulta(item.consulta_id) : null"
               :title="item.tipo === 'consulta' ? 'Clique para editar a consulta' : ''">
            <div class="timestamp-ago" :class="getHowMuchTimeClass(item)">{{ $filters.howMuchTime(item.data + ' ' + item.horario, { type: 'date' }) }}</div>
            <div class="timestamp-date">{{ $filters.dateDmy(item.data) }}</div>
            <div class="timestamp-time">{{ formatTime(item.horario) }}</div>
          </div>

          <div class="timeline-badge"
               :class="[getTimelineBadgeClass(item), getTimelineBadgeColorClass(item), { 'clickable-badge': item.tipo === 'consulta' }]"
               @click="item.tipo === 'consulta' ? editarConsulta(item.consulta_id) : null"
               :title="item.tipo === 'consulta' ? 'Clique para editar a consulta' : ''">
            <font-awesome-icon :icon="['fas', getTimelineIcon(item)]" />
          </div>
          <div class="timeline-panel" :class="getTimelinePanelClass(item)">
            <!-- Consulta com layout compacto -->
            <div v-if="item.tipo === 'consulta'" class="consulta-compacta">
              <div class="consulta-header">
                <div class="consulta-info-principal">
                  <div class="data-titulo">
                    <h6 class="timeline-title mb-2">
                      Consulta -
                      <span class="categoria-badge-inline" :class="getCategoriaClass(item.categoria)">
                        <font-awesome-icon :icon="['fas', getCategoriaIcon(item.categoria)]" class="me-1" />
                        {{ getCategoriaNome(item.categoria) }}
                      </span> -
                      <span class="ortodontista-nome-inline">Dr. {{ getDentistaName(item.dentista_id) }}</span>
                    </h6>

                    <!-- Histórico da consulta e próxima consulta -->
                    <div class="consulta-historico-lado-a-lado">
                      <!-- Histórico da consulta -->
                      <div class="historico-coluna">
                        <div v-if="item.historico_consulta && item.historico_consulta.trim() && !editandoHistorico[item.consulta_id]" class="historico-preenchido">
                          <div class="historico-label-editavel" @click="confirmarEdicaoHistorico(item.consulta_id, item.historico_consulta)" title="Clique para editar o histórico">
                            <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-1" />
                            Histórico
                            <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon" />
                          </div>
                          <p class="historico-texto-editavel" @click="confirmarEdicaoHistorico(item.consulta_id, item.historico_consulta)">{{ item.historico_consulta }}</p>
                        </div>
                        <div v-else class="historico-editando">
                          <div class="historico-label-editavel editando">
                            <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-1" />
                            Histórico
                            <span class="editando-indicator">Editando...</span>
                          </div>
                          <div class="textarea-container">
                            <textarea
                              :ref="`historico-${item.consulta_id}`"
                              v-model="textoHistorico[item.consulta_id]"
                              class="form-control historico-textarea-editando"
                              :placeholder="item.historico_consulta && item.historico_consulta.trim() ? 'Edite o histórico da consulta...' : 'Digite o histórico desta consulta...'"
                              rows="3"
                              @keydown.ctrl.enter="salvarHistorico(item.consulta_id)"
                              @keydown.esc="cancelarEdicaoHistorico(item.consulta_id)"
                            ></textarea>
                            <div class="textarea-actions">
                              <button class="btn btn-sm btn-success me-2" @click="salvarHistorico(item.consulta_id)" :disabled="salvandoHistorico[item.consulta_id]">
                                <font-awesome-icon :icon="['fas', salvandoHistorico[item.consulta_id] ? 'spinner' : 'check']" :spin="salvandoHistorico[item.consulta_id]" />
                                Salvar
                              </button>
                              <button class="btn btn-sm btn-outline-secondary" @click="cancelarEdicaoHistorico(item.consulta_id)">
                                <font-awesome-icon :icon="['fas', 'times']" />
                                Cancelar
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Próxima consulta -->
                      <div class="proxima-coluna">
                        <div v-if="item.proxima_consulta && item.proxima_consulta.trim() && !editandoProxima[item.consulta_id]" class="proxima-preenchida">
                          <div class="historico-label-editavel proxima-label" @click="confirmarEdicaoProxima(item.consulta_id, item.proxima_consulta)" title="Clique para editar o que fazer na próxima consulta">
                            <font-awesome-icon :icon="['fas', 'arrow-right']" class="me-1" />
                            Próxima consulta
                            <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon" />
                          </div>
                          <p class="historico-texto-editavel proxima-texto" @click="confirmarEdicaoProxima(item.consulta_id, item.proxima_consulta)">{{ item.proxima_consulta }}</p>
                        </div>
                        <div v-else class="proxima-editando">
                          <div class="historico-label-editavel proxima-label editando">
                            <font-awesome-icon :icon="['fas', 'arrow-right']" class="me-1" />
                            Próxima consulta
                            <span class="editando-indicator">Editando...</span>
                          </div>
                          <div class="textarea-container">
                            <textarea
                              :ref="`proxima-${item.consulta_id}`"
                              v-model="textoProxima[item.consulta_id]"
                              class="form-control proxima-textarea-editando"
                              :placeholder="item.proxima_consulta && item.proxima_consulta.trim() ? 'Edite o que fazer na próxima consulta...' : 'Digite o que fazer na próxima consulta...'"
                              rows="3"
                              @keydown.ctrl.enter="salvarProxima(item.consulta_id)"
                              @keydown.esc="cancelarEdicaoProxima(item.consulta_id)"
                            ></textarea>
                            <div class="textarea-actions">
                              <button class="btn btn-sm btn-warning me-2" @click="salvarProxima(item.consulta_id)" :disabled="salvandoProxima[item.consulta_id]">
                                <font-awesome-icon :icon="['fas', salvandoProxima[item.consulta_id] ? 'spinner' : 'check']" :spin="salvandoProxima[item.consulta_id]" />
                                Salvar
                              </button>
                              <button class="btn btn-sm btn-outline-secondary" @click="cancelarEdicaoProxima(item.consulta_id)">
                                <font-awesome-icon :icon="['fas', 'times']" />
                                Cancelar
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Removido o botão de editar - agora o clique é na data/horário ou ícone -->
                </div>
              </div>
            </div>

            <!-- Histórico manual -->
            <div v-else class="historico-manual">
              <div class="historico-header">
                <h6 class="timeline-title mb-2">{{ getTimelineTitle(item) }}</h6>
              </div>

              <!-- Descrição editável -->
              <div class="historico-item">
                <div v-if="!editandoHistoricoSimples[item.id]" class="historico-content-readonly">
                  <div class="historico-label-editavel" @click="confirmarEdicaoHistoricoSimples(item)">
                    <font-awesome-icon :icon="['fas', 'edit']" class="me-1" />
                    Descrição
                    <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon" />
                  </div>
                  <p class="historico-texto-editavel" @click="confirmarEdicaoHistoricoSimples(item)">{{ item.descricao }}</p>
                </div>
                <div v-else class="historico-editando">
                  <div class="historico-label-editavel editando">
                    <font-awesome-icon :icon="['fas', 'edit']" class="me-1" />
                    Descrição
                    <span class="editando-indicator">Editando...</span>
                  </div>
                  <div class="textarea-container">
                    <textarea
                      v-model="textoHistoricoSimples[item.id]"
                      class="form-control historico-textarea"
                      rows="3"
                      :placeholder="'Descreva o que aconteceu...'"
                    ></textarea>
                    <div class="textarea-actions">
                      <button
                        @click="salvarHistoricoSimples(item)"
                        class="btn btn-primary btn-sm me-2"
                        :disabled="salvandoHistoricoSimples[item.id] || !textoHistoricoSimples[item.id]?.trim()"
                      >
                        <span v-if="salvandoHistoricoSimples[item.id]" class="spinner-border spinner-border-sm me-1" role="status"></span>
                        <font-awesome-icon v-else :icon="['fas', 'save']" class="me-1" />
                        Salvar
                      </button>
                      <button
                        @click="cancelarEdicaoHistoricoSimples(item.id)"
                        class="btn btn-outline-secondary btn-sm"
                        :disabled="salvandoHistoricoSimples[item.id]"
                      >
                        <font-awesome-icon :icon="['fas', 'times']" class="me-1" />
                        Cancelar
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para registrar histórico -->
    <RegistrarHistoricoModal ref="registrarHistoricoModal" @historico-salvo="recarregarDados" />
  </div>
</template>

<style scoped>
.consultas-historico-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px;
}

/* Container centralizado */
.timeline-wrapper {
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 2rem;
  text-align: center;
  background: #fff;
  border-radius: 16px;
  margin: 0.5rem 0;
  border: 2px dashed #e2e8f0;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.empty-state-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.empty-state-message .icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.empty-state-message .empty-state-icon {
  font-size: 2rem;
  color: #fff;
}

.empty-state-message p {
  color: #64748b;
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
}

/* Header actions - centralizadas */
.header-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

/* Dropdown customizado para registrar histórico */
.dropdown-custom {
  position: relative;
  display: inline-block;
}

.dropdown-menu-custom {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 8px 0;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  min-width: 200px;
}

.dropdown-menu-custom.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item-custom {
  padding: 12px 20px;
  font-size: 0.9rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.dropdown-item-custom:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #007bff;
  transform: translateX(4px);
}

.dropdown-item-custom .fa-icon {
  color: #007bff;
  width: 16px;
}

/* Botão elegante para registrar histórico */
.btn-registrar-historico {
  border: 1px solid #007bff;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  font-size: 0.85rem;
  color: #007bff;
  background: white;
  box-shadow: 0 1px 4px rgba(0, 123, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-registrar-historico:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 123, 255, 0.2);
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-registrar-historico:active {
  transform: translateY(0);
  box-shadow: 0 1px 6px rgba(0, 123, 255, 0.15);
}

.btn-registrar-historico .btn-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-registrar-historico .dropdown-icon {
  font-size: 0.7rem;
  transition: transform 0.3s ease;
  order: -1; /* Coloca o ícone dropdown na frente */
}

.btn-registrar-historico.dropdown-aberto .dropdown-icon,
.btn-registrar-historico:hover .dropdown-icon {
  transform: rotate(180deg);
}



.btn-registrar-historico .btn-text {
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Timeline styles */
.timeline-container {
  position: relative;
  padding: 10px 0;
}

.timeline-container:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  width: 3px;
  background: linear-gradient(to bottom, rgba(0, 123, 255, 0.3), rgba(0, 123, 255, 0.7));
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(0, 123, 255, 0.2);
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
  animation: fadeIn 0.6s ease-out;
}

.timeline-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-bottom: 20px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(15px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Estilos para campos editáveis nos itens temporários */
.field-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Título da consulta temporária */
.consulta-title {
  color: #2c3e50;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
}

/* Header row para títulos e ações */
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.title-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.actions-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Seletor de categoria inline */
.categoria-selector {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.categoria-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

.categoria-select {
  width: auto;
  min-width: 200px;
  font-size: 0.8rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
}

/* Badge pequena */
.badge-small {
  font-size: 0.65rem;
  padding: 0.25em 0.5em;
}

/* Histórico simples editável */
.historico-content-readonly {
  cursor: pointer;
}

.historico-content-readonly:hover .historico-label-editavel {
  color: #007bff;
}

.historico-content-readonly:hover .edit-icon {
  opacity: 1;
}

.label-icon {
  color: #007bff;
  font-size: 0.7rem;
}

.field-input {
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: border-color 0.2s ease;
}

.field-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* Ações dos itens temporários */
.temp-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Animação para itens temporários */
.timeline-item:has(.temp-actions) {
  animation: slideInDown 0.4s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Timestamp destacada */
.timeline-timestamp {
  position: absolute;
  top: 8px;
  left: -120px;
  width: 100px;
  text-align: right;
  z-index: 50;
}

.timestamp-ago {
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 4px;
  padding: 2px 6px;
  border-radius: 4px;
}

.timestamp-ago.howmuchtime-today {
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

.timestamp-ago.howmuchtime-past {
  color: #fd7e14;
  background-color: rgba(253, 126, 20, 0.1);
}

.timestamp-ago.howmuchtime-future {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.timestamp-date {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  line-height: 1.2;
}

.timestamp-time {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6c757d;
  line-height: 1.2;
}

/* Timestamp clicável para consultas */
.clickable-timestamp {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
}

.clickable-timestamp:hover {
  background-color: rgba(0, 123, 255, 0.1);
  transform: translateX(-2px);
}

.clickable-timestamp:hover .timestamp-date,
.clickable-timestamp:hover .timestamp-time {
  color: #007bff;
  font-weight: 600;
}

.timeline-badge {
  position: absolute;
  top: 12px;
  left: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  background-color: white;
  border: 3px solid #007bff;
  color: #007bff;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
}

.timeline-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 18px rgba(0, 123, 255, 0.4);
}

/* Badge clicável para consultas */
.clickable-badge {
  cursor: pointer;
}

.clickable-badge:hover {
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.5);
}

.timeline-panel {
  position: relative;
  margin-left: 60px;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.timeline-panel:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 123, 255, 0.2);
  transform: translateY(-2px);
}

.timeline-panel:before {
  content: '';
  position: absolute;
  top: 15px;
  left: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid rgba(0, 0, 0, 0.08);
}

.timeline-panel:after {
  content: '';
  position: absolute;
  top: 15px;
  left: -9px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.timeline-title {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 0.95rem;
}

.timeline-date {
  color: #6c757d;
  margin: 0;
  font-size: 0.8rem;
}

.tempo-passado {
  color: #28a745;
  font-weight: 500;
}

/* Consulta styles */
.consulta-badge {
  border-color: #28a745;
  color: #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.consulta-panel {
  border-left: 3px solid #28a745;
}

.consulta-panel:before {
  border-right-color: #28a745;
}

/* Badge para itens de hoje - com !important para sobrescrever outras classes */
.timeline-badge.hoje-badge {
  border-color: #198754 !important;
  color: #198754 !important;
  box-shadow: 0 2px 6px rgba(25, 135, 84, 0.3) !important;
  background: linear-gradient(135deg, #d1eddb, #f8fff9) !important;
}

.timeline-badge.hoje-badge:hover {
  box-shadow: 0 6px 18px rgba(25, 135, 84, 0.4) !important;
}

/* Layout compacto para consultas */
.consulta-compacta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.consulta-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.consulta-info-principal {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.data-titulo {
  flex: 1;
}

.consulta-actions {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

/* Ortodontista inline no título */
.ortodontista-nome-inline {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
  margin-right: 6px;
}

/* Categoria inline no título */
.categoria-badge-inline {
  display: inline-flex;
  align-items: center;
  font-size: 7pt;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.categoria-badge-inline:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.categoria-badge-inline.categoria-badge-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-dark {
  background: linear-gradient(135deg, #343a40, #1d2124);
  color: #fff;
}

/* Histórico lado a lado */
.consulta-historico-lado-a-lado {
  margin-top: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.historico-coluna,
.proxima-coluna {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.historico-label-editavel {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 6px;
  border-radius: 6px;
  position: relative;
}

.historico-label-editavel:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.historico-label-editavel .edit-icon {
  opacity: 0;
  margin-left: auto;
  font-size: 0.7rem;
  transition: opacity 0.3s ease;
}

.historico-label-editavel:hover .edit-icon {
  opacity: 1;
}

.historico-texto-editavel {
  font-size: 0.85rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.historico-texto-editavel:hover {
  background-color: #e9ecef;
  border-left-color: #007bff;
  transform: translateX(2px);
}

/* Estilos para campos vazios */
.historico-textarea-vazio,
.proxima-textarea-vazia {
  font-size: 0.85rem;
  color: #6c757d;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  resize: none;
  min-height: 60px;
}

.historico-textarea-vazio:hover,
.proxima-textarea-vazia:hover {
  background-color: #e9ecef;
  border-color: #007bff;
  border-style: solid;
}

.historico-textarea-vazio::placeholder,
.proxima-textarea-vazia::placeholder {
  color: #adb5bd;
  font-style: italic;
}

.proxima-textarea-vazia {
  background-color: #fff9e6;
  border-color: #ffc107;
}

.proxima-textarea-vazia:hover {
  background-color: #fff3cd;
  border-color: #e0a800;
}

/* Estilos para edição inline */
.historico-editando,
.proxima-editando {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.historico-label-editavel.editando {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  padding: 6px 10px;
  border-radius: 6px;
}

.proxima-label.editando {
  background-color: #fff8e1;
  color: #f57c00;
  border-color: #ffcc02;
}

.editando-indicator {
  margin-left: auto;
  font-size: 0.7rem;
  font-weight: 500;
  color: #1976d2;
  animation: pulse 1.5s infinite;
}

.proxima-label .editando-indicator {
  color: #f57c00;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.textarea-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.historico-textarea-editando,
.proxima-textarea-editando {
  font-size: 0.85rem;
  line-height: 1.4;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 80px;
}

.historico-textarea-editando:focus,
.proxima-textarea-editando:focus {
  outline: none;
  border-color: #0056b3;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.proxima-textarea-editando {
  border-color: #ffc107;
  background-color: #fff9e6;
}

.proxima-textarea-editando:focus {
  border-color: #e0a800;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
}

.textarea-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.textarea-actions .btn {
  font-size: 0.75rem;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.textarea-actions .btn:hover {
  transform: translateY(-1px);
}

.proxima-coluna .historico-label-editavel.proxima-label {
  color: #ffc107;
}

.proxima-coluna .historico-label-editavel.proxima-label:hover {
  color: #e0a800;
}

.proxima-coluna .historico-texto-editavel.proxima-texto {
  background-color: #fff9e6;
  border-left-color: #ffc107;
}

.proxima-coluna .historico-texto-editavel.proxima-texto:hover {
  background-color: #fff3cd;
  border-left-color: #e0a800;
}

/* Histórico manual */
.historico-manual {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.historico-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.historico-descricao {
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.5;
  margin: 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

/* Categoria badges */
.categoria-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.65rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.categoria-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.categoria-badge-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.categoria-badge-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: #fff;
}

.categoria-badge-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #fff;
}

.categoria-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: #fff;
}

.categoria-badge-dark {
  background: linear-gradient(135deg, #343a40, #1d2124);
  color: #fff;
}

/* Valor badge */
.valor-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  padding: 0.3rem 0.7rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  border-radius: 10px;
  font-weight: 700;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.valor-badge:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Status badges */
.badge {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Futuro item styles */
.futuro-item {
  position: relative;
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-out;
}

.futuro-badge {
  border-color: #ffc107;
  color: #ffc107;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.futuro-panel {
  border-left: 3px solid #ffc107;
  background: linear-gradient(135deg, #fff9e6, #fffbf0);
  border: 1px solid #ffeaa7;
}

.futuro-panel:before {
  border-right-color: #ffeaa7;
}

.futuro-panel:after {
  border-right-color: #fff9e6;
}

.futuro-indicator {
  display: inline-block;
  font-size: 0.65rem;
  font-weight: 500;
  color: #fff;
  background-color: #ffc107;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  text-transform: uppercase;
  vertical-align: middle;
  letter-spacing: 0.5px;
}

/* Consulta histórico styles */
.consulta-historico {
  border-top: 1px solid #e9ecef;
  padding-top: 1rem;
  margin-top: 1rem;
}

.historico-secao {
  margin-bottom: 0;
}

.historico-secao-titulo {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.historico-secao-conteudo {
  margin-bottom: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
  padding-left: 24px;
}

.proxima-consulta-titulo {
  color: #ffc107;
}

.proxima-consulta-conteudo {
  background-color: #fff9e6;
  border-left: 3px solid #ffc107;
  padding: 8px 12px;
  border-radius: 4px;
  margin-left: 0;
  padding-left: 36px;
}

/* Action buttons */
.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

/* Loading spinner styling */
.spinner-border {
  color: #667eea;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timeline-wrapper {
    max-width: 100%;
    padding: 0 10px;
  }

  .timeline-container {
    padding: 5px 0;
  }

  /* Timestamp em mobile - mover para dentro do card */
  .timeline-timestamp {
    position: static;
    width: auto;
    text-align: left;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
  }

  .timestamp-ago {
    display: block;
    margin-bottom: 4px;
    margin-right: 0;
  }

  .timestamp-date,
  .timestamp-time {
    display: inline;
    margin-right: 8px;
  }

  .timeline-panel {
    margin-left: 45px;
    padding: 12px 16px;
  }

  .timeline-badge {
    width: 30px;
    height: 30px;
    font-size: 0.8em;
    top: 10px;
  }

  .timeline-container:before {
    left: 14px;
  }

  .timeline-panel:before {
    top: 12px;
    left: -8px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgba(0, 0, 0, 0.08);
  }

  .timeline-panel:after {
    top: 12px;
    left: -7px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #fff;
  }

  .consulta-info-principal {
    flex-direction: column;
    gap: 8px;
  }

  .consulta-actions {
    align-self: flex-end;
  }

  .consulta-historico-lado-a-lado {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .categoria-badge-inline {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .categoria-badge {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
  }

  .valor-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.6rem;
  }

  .btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .historico-texto-editavel {
    font-size: 0.8rem;
  }

  .historico-label-editavel {
    font-size: 0.75rem;
  }

  .timeline-title {
    font-size: 0.9rem;
  }

  .timeline-date {
    font-size: 0.75rem;
  }

  .historico-textarea-vazio,
  .proxima-textarea-vazia {
    min-height: 50px;
    font-size: 0.8rem;
  }

  .btn-registrar-historico {
    padding: 6px 14px;
    font-size: 0.8rem;
  }

  .btn-registrar-historico .btn-content {
    gap: 5px;
  }

  .btn-registrar-historico .btn-icon {
    font-size: 0.8rem;
  }

  .textarea-actions {
    flex-direction: column;
    gap: 6px;
  }

  .textarea-actions .btn {
    width: 100%;
  }

  .historico-textarea-editando,
  .proxima-textarea-editando {
    min-height: 70px;
    font-size: 0.8rem;
  }
}
</style>

<script>
import moment from 'moment';
import {
  getHistoricosPaciente,
  getHistoricosConsulta,
  criarHistoricoPaciente,
  atualizarHistoricoPaciente
} from "@/services/historicoPacienteService";
import { getConsultasByPaciente, novaConsulta } from "@/services/consultasService";
import RegistrarHistoricoModal from "@/components/RegistrarHistoricoModal.vue";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "ConsultasHistoricoTimeline",
  components: {
    RegistrarHistoricoModal
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    dentistas: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:consultas', 'editar-consulta', 'ver-historico'],
  data() {
    return {
      isLoading: false,
      historicos: [],
      consultas: [],
      proximaConsultaInfo: null,
      // Estados de edição inline
      editandoHistorico: {},
      editandoProxima: {},
      textoHistorico: {},
      textoProxima: {},
      salvandoHistorico: {},
      salvandoProxima: {},
      // Estados de edição para históricos simples
      editandoHistoricoSimples: {},
      textoHistoricoSimples: {},
      salvandoHistoricoSimples: {},
      // Cache de históricos de consulta para evitar múltiplas requisições
      historicosConsulta: {},
      // Itens temporários sendo criados inline
      itensTemporarios: [],
      // Estado do dropdown customizado
      dropdownAberto: false,
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
    itensTimeline() {
      const items = [];

      // Adicionar consultas
      this.consultas.forEach(consulta => {
        const item = {
          id: `consulta_${consulta.id}`,
          data: consulta.horario.split(' ')[0],
          horario: consulta.horario.split(' ')[1] || '00:00:00',
          tipo: 'consulta',
          consulta_id: consulta.id,
          categoria: consulta.categoria,
          dentista_id: consulta.dentista_id,
          valor: consulta.valor,
          status: consulta.status,
          historico_consulta: '',
          proxima_consulta: ''
        };

        // Buscar histórico da consulta nos históricos
        const historicoConsulta = this.historicos.find(h => 
          h.codigo_acao === 'alteracao_consulta' && 
          h.consulta_id === consulta.id
        );

        if (historicoConsulta && historicoConsulta.modificacoes) {
          try {
            let modificacoes = typeof historicoConsulta.modificacoes === 'string'
              ? JSON.parse(historicoConsulta.modificacoes)
              : historicoConsulta.modificacoes;

            if (Array.isArray(modificacoes)) {
              const historicoMod = modificacoes.find(m => m.titulo === 'Histórico da consulta');
              const proximaMod = modificacoes.find(m => m.titulo === 'O que fazer na próxima consulta');
              
              if (historicoMod && historicoMod.descricao) {
                item.historico_consulta = historicoMod.descricao;
              }
              if (proximaMod && proximaMod.descricao) {
                item.proxima_consulta = proximaMod.descricao;
              }
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
          }
        }

        items.push(item);
      });

      // Adicionar históricos manuais
      this.historicos.forEach(historico => {
        if (historico.codigo_acao !== 'alteracao_consulta') {
          items.push({
            ...historico,
            tipo: 'historico_manual'
          });
        }
      });

      // Ordenar por data/horário (mais recente primeiro)
      return items.sort((a, b) => {
        const dateA = new Date(`${a.data} ${a.horario}`);
        const dateB = new Date(`${b.data} ${b.horario}`);
        return dateB - dateA;
      });
    },
    ultimaConsultaSemProximaConsulta() {
      // Verificar se a última consulta (mais recente) não tem o campo "O que fazer na próxima consulta" preenchido
      const consultasOrdenadas = this.itensTimeline.filter(item => item.tipo === 'consulta');
      if (consultasOrdenadas.length === 0) return false;

      const ultimaConsulta = consultasOrdenadas[0]; // Primeira após ordenação (mais recente)
      return !ultimaConsulta.proxima_consulta || !ultimaConsulta.proxima_consulta.trim();
    }
  },
  mounted() {
    this.fetchConsultas();
  },
  watch: {
    'paciente.id': {
      handler(newVal) {
        if (newVal) {
          this.fetchConsultas();
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchConsultas() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoading = true;
      try {
        // Carregar consultas
        const consultas = await getConsultasByPaciente(this.paciente.id);
        if (consultas && Array.isArray(consultas)) {
          this.consultas = consultas;
          this.$emit('update:consultas', consultas);
        }

        // Carregar históricos
        const historicos = await getHistoricosPaciente(this.paciente.id);
        if (historicos && Array.isArray(historicos)) {
          this.historicos = historicos;
        }

        // Buscar informação da próxima consulta
        await this.buscarProximaConsultaInfo();
      } catch (error) {
        console.error('Erro ao buscar dados do paciente:', error);
        cSwal.cError("Erro ao carregar dados do paciente.");
      } finally {
        this.isLoading = false;
      }
    },
    async buscarProximaConsultaInfo() {
      // Buscar consultas anteriores ou iguais ao dia de hoje que tenham orientação para próxima consulta
      const hoje = moment().format('YYYY-MM-DD');

      // Filtrar consultas anteriores ou iguais a hoje
      const consultasPassadas = this.consultas
        .filter(consulta => consulta.horario.split(' ')[0] <= hoje)
        .sort((a, b) => {
          const dateA = new Date(a.horario);
          const dateB = new Date(b.horario);
          return dateB - dateA; // Mais recente primeiro
        });

      // Buscar orientação da consulta mais recente que tenha o campo preenchido
      for (const consulta of consultasPassadas) {
        const historicoConsulta = this.historicos.find(h =>
          h.codigo_acao === 'alteracao_consulta' &&
          h.consulta_id === consulta.id
        );

        if (historicoConsulta && historicoConsulta.modificacoes) {
          try {
            let modificacoes = typeof historicoConsulta.modificacoes === 'string'
              ? JSON.parse(historicoConsulta.modificacoes)
              : historicoConsulta.modificacoes;

            if (Array.isArray(modificacoes)) {
              const proximaConsulta = modificacoes.find(m => m.titulo === "O que fazer na próxima consulta");
              if (proximaConsulta && proximaConsulta.descricao && proximaConsulta.descricao.trim()) {
                this.proximaConsultaInfo = proximaConsulta.descricao;
                return; // Encontrou, pode parar
              }
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
          }
        }
      }

      // Se não encontrou nenhuma orientação, limpar
      this.proximaConsultaInfo = null;
    },
    formatTime(dateTime) {
      if (!dateTime) return '-';

      try {
        let timeString = '';

        // Se for uma string de data completa, extrair apenas a parte da hora
        if (typeof dateTime === 'string') {
          // Verificar se é uma data completa (YYYY-MM-DD HH:MM:SS)
          if (dateTime.includes('T') || dateTime.includes(' ')) {
            const date = new Date(dateTime);
            if (!isNaN(date.getTime())) {
              timeString = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            }
          }

          // Se for apenas um horário (HH:MM:SS)
          if (dateTime.includes(':')) {
            const timeParts = dateTime.split(':');
            if (timeParts.length >= 2) {
              timeString = `${timeParts[0]}:${timeParts[1]}`;
            }
          }
        }

        // Se for um objeto Date
        if (dateTime instanceof Date) {
          timeString = dateTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        // Tentar com moment como último recurso se ainda não temos timeString
        if (!timeString) {
          timeString = moment(dateTime).format('HH:mm');
        }

        // Adicionar "h" no final
        return timeString ? `${timeString}h` : '-';
      } catch (error) {
        console.error('Erro ao formatar horário:', error);
        return '-';
      }
    },
    getTimelineTitle(item) {
      if (item.tipo === 'consulta') {
        return `Consulta - ${this.getCategoriaNome(item.categoria)}`;
      }

      switch (item.codigo_acao) {
        case 'registro_manual':
          return 'Registro de histórico';
        case 'inicio_tratamento':
          return 'Início do tratamento';
        case 'alteracao_aparelho':
          return 'Alteração de aparelho';
        case 'ajuste_aparelho':
          return 'Ajuste de aparelho';
        case 'remocao_aparelho':
          return 'Remoção de aparelho';
        default:
          return item.codigo_acao ? item.codigo_acao : 'Registro de histórico';
      }
    },
    getTimelineIcon(item) {
      if (item.tipo === 'consulta') {
        // Para itens temporários, usar ícone da categoria
        if (item.temporario && item.categoria) {
          const categoria = this.categorias.find(cat => cat.valor === item.categoria);
          return categoria ? categoria.icone : 'calendar-check';
        }
        return 'calendar-check';
      }

      // Para histórico temporário
      if (item.temporario) {
        return 'clipboard-list';
      }

      switch (item.codigo_acao) {
        case 'registro_manual':
          return 'edit';
        case 'inicio_tratamento':
          return 'play-circle';
        case 'alteracao_aparelho':
        case 'ajuste_aparelho':
          return 'wrench';
        case 'remocao_aparelho':
          return 'minus-circle';
        default:
          return 'circle';
      }
    },
    getTimelineBadgeClass(item) {
      if (item.tipo === 'consulta') {
        return 'consulta-badge';
      }
      return '';
    },
    getTimelineBadgeColorClass(item) {
      const howMuchTimeClass = this.getHowMuchTimeClass(item);
      if (howMuchTimeClass === 'howmuchtime-today') {
        return 'hoje-badge';
      }
      return '';
    },
    getTimelinePanelClass(item) {
      if (item.tipo === 'consulta') {
        return 'consulta-panel';
      }
      return '';
    },
    getDentistaName(dentistaId) {
      if (!dentistaId) return '-';

      const dentista = this.dentistas.find(d => d.id === dentistaId || d.id === parseInt(dentistaId));
      return dentista ? dentista.nome : `Dentista #${dentistaId}`;
    },
    formatCurrency(value) {
      if (!value && value !== 0) return '-';

      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },
    getStatusClass(status) {
      const statusClasses = {
        'agendada': 'bg-info',
        'realizada': 'bg-success',
        'cancelada': 'bg-danger',
        'reagendada': 'bg-warning'
      };

      return statusClasses[status] || 'bg-secondary';
    },
    getStatusText(status) {
      const statusTexts = {
        'agendada': 'AGENDADA',
        'realizada': 'REALIZADA',
        'cancelada': 'CANCELADA',
        'reagendada': 'REAGENDADA'
      };

      return statusTexts[status] || status.toUpperCase();
    },
    getCategoriaData(categoria) {
      return this.categorias.find(cat => cat.valor === categoria) ||
             { valor: categoria, nome: categoria, cor: 'secondary', icone: 'question' };
    },
    getCategoriaNome(categoria) {
      if (!categoria) return '-';
      return this.getCategoriaData(categoria).nome;
    },
    getCategoriaIcon(categoria) {
      if (!categoria) return 'question';
      return this.getCategoriaData(categoria).icone;
    },
    getCategoriaClass(categoria) {
      if (!categoria) return 'categoria-badge-secondary';
      const cor = this.getCategoriaData(categoria).cor;
      return `categoria-badge-${cor}`;
    },
    getHowMuchTimeClass(item) {
      if (!item.data) return 'howmuchtime-future';

      // Usar moment para trabalhar com datas de forma mais segura
      const itemDate = moment(item.data);
      const today = moment().startOf('day');

      if (itemDate.isSame(today, 'day')) {
        // É hoje - sempre retornar verde, independente do horário
        return 'howmuchtime-today';
      } else if (itemDate.isAfter(today)) {
        // Data futura (amanhã ou depois)
        return 'howmuchtime-future';
      } else {
        // Data passada (ontem ou antes)
        return 'howmuchtime-past';
      }
    },
    editarConsulta(id) {
      this.$emit('editar-consulta', id);
    },
    verHistorico(id) {
      this.$emit('ver-historico', id);
    },
    // Métodos de edição inline
    confirmarEdicaoHistorico(consultaId, textoAtual) {
      cSwal.cConfirm(
        'Deseja editar o Histórico desta consulta?',
        () => {
          this.iniciarEdicaoHistorico(consultaId, textoAtual);
        },
        {
          title: 'Editar Histórico',
          confirmButtonText: 'Sim, editar',
          cancelButtonText: 'Cancelar'
        }
      );
    },
    confirmarEdicaoProxima(consultaId, textoAtual) {
      cSwal.cConfirm(
        'Deseja editar as orientações para a próxima consulta?',
        () => {
          this.iniciarEdicaoProxima(consultaId, textoAtual);
        },
        {
          title: 'Editar Próxima Consulta',
          confirmButtonText: 'Sim, editar',
          cancelButtonText: 'Cancelar'
        }
      );
    },
    iniciarEdicaoHistorico(consultaId, textoAtual = '') {
      this.editandoHistorico[consultaId] = true;
      this.textoHistorico[consultaId] = textoAtual;
      this.$nextTick(() => {
        const textarea = this.$refs[`historico-${consultaId}`];
        if (textarea && textarea[0]) {
          textarea[0].focus();
        }
      });
    },
    iniciarEdicaoProxima(consultaId, textoAtual = '') {
      this.editandoProxima[consultaId] = true;
      this.textoProxima[consultaId] = textoAtual;
      this.$nextTick(() => {
        const textarea = this.$refs[`proxima-${consultaId}`];
        if (textarea && textarea[0]) {
          textarea[0].focus();
        }
      });
    },
    cancelarEdicaoHistorico(consultaId) {
      this.editandoHistorico[consultaId] = false;
      this.textoHistorico[consultaId] = '';
    },
    cancelarEdicaoProxima(consultaId) {
      this.editandoProxima[consultaId] = false;
      this.textoProxima[consultaId] = '';
    },
    async salvarHistorico(consultaId) {
      this.salvandoHistorico[consultaId] = true;

      try {
        const novoTextoHistorico = this.textoHistorico[consultaId];

        // Buscar ou criar o histórico da consulta
        await this.salvarCampoHistoricoConsulta(consultaId, 'Histórico da consulta', novoTextoHistorico);

        // Atualizar o item na timeline
        const item = this.itensTimeline.find(i => i.consulta_id === consultaId);
        if (item) {
          item.historico_consulta = novoTextoHistorico;
        }

        // Finalizar edição
        this.editandoHistorico[consultaId] = false;
        this.textoHistorico[consultaId] = '';

        cSwal.cSuccess("Histórico salvo com sucesso!");
      } catch (error) {
        console.error('Erro ao salvar histórico:', error);
        cSwal.cError("Erro ao salvar histórico. Tente novamente.");
      } finally {
        this.salvandoHistorico[consultaId] = false;
      }
    },
    async salvarProxima(consultaId) {
      this.salvandoProxima[consultaId] = true;

      try {
        const novoTextoProxima = this.textoProxima[consultaId];

        // Buscar ou criar o histórico da consulta
        await this.salvarCampoHistoricoConsulta(consultaId, 'O que fazer na próxima consulta', novoTextoProxima);

        // Atualizar o item na timeline
        const item = this.itensTimeline.find(i => i.consulta_id === consultaId);
        if (item) {
          item.proxima_consulta = novoTextoProxima;
        }

        // Atualizar a informação da próxima consulta no topo se necessário
        if (novoTextoProxima && novoTextoProxima.trim()) {
          this.proximaConsultaInfo = novoTextoProxima;
        }

        // Finalizar edição
        this.editandoProxima[consultaId] = false;
        this.textoProxima[consultaId] = '';

        cSwal.cSuccess("Próxima consulta salva com sucesso!");
      } catch (error) {
        console.error('Erro ao salvar próxima consulta:', error);
        cSwal.cError("Erro ao salvar próxima consulta. Tente novamente.");
      } finally {
        this.salvandoProxima[consultaId] = false;
      }
    },
    async salvarCampoHistoricoConsulta(consultaId, tituloCampo, novoTexto) {
      try {
        // Buscar histórico existente da consulta
        let historicoConsulta = this.historicosConsulta[consultaId];

        if (!historicoConsulta) {
          // Buscar do backend se não estiver em cache
          const historicos = await getHistoricosConsulta(this.paciente.id, consultaId);
          historicoConsulta = historicos.find(h => h.codigo_acao === 'alteracao_consulta');
          this.historicosConsulta[consultaId] = historicoConsulta;
        }

        let modificacoes = [];

        // Se já existe um histórico, pegar as modificações existentes
        if (historicoConsulta && historicoConsulta.modificacoes) {
          try {
            modificacoes = typeof historicoConsulta.modificacoes === 'string'
              ? JSON.parse(historicoConsulta.modificacoes)
              : historicoConsulta.modificacoes;
          } catch (e) {
            console.error('Erro ao parsear modificações:', e);
            modificacoes = [];
          }
        }

        // Garantir que temos os dois campos fixos
        const titulosFixos = ['Histórico da consulta', 'O que fazer na próxima consulta'];
        if (modificacoes.length === 0) {
          modificacoes = titulosFixos.map(titulo => ({
            titulo: titulo,
            descricao: ''
          }));
        }

        // Atualizar o campo específico
        const indexCampo = titulosFixos.indexOf(tituloCampo);
        if (indexCampo !== -1) {
          modificacoes[indexCampo] = {
            titulo: tituloCampo,
            descricao: novoTexto
          };
        }

        // Preparar dados para salvar
        const historicoData = {
          paciente_id: this.paciente.id,
          consulta_id: consultaId,
          data: moment().format('YYYY-MM-DD'),
          horario: moment().format('HH:mm:ss'),
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoes)
        };

        let response;

        // Se já existe um histórico, atualizar
        if (historicoConsulta && historicoConsulta.id) {
          response = await atualizarHistoricoPaciente(historicoConsulta.id, historicoData);
        } else {
          // Criar um novo histórico
          response = await criarHistoricoPaciente(historicoData);
        }

        // Atualizar cache
        if (response) {
          this.historicosConsulta[consultaId] = response;
        }

        return response;
      } catch (error) {
        console.error('Erro ao salvar campo do histórico:', error);
        throw error;
      }
    },
    // Métodos para controlar dropdown customizado
    abrirDropdown() {
      this.dropdownAberto = true;
    },
    fecharDropdown() {
      this.dropdownAberto = false;
    },

    formatarDataTimestamp(data) {
      return moment(data).format('DD/MM/YYYY');
    },
    // Métodos para edição de históricos simples
    confirmarEdicaoHistoricoSimples(item) {
      if (!item.descricao || !item.descricao.trim()) {
        // Se estiver vazio, editar diretamente
        this.editarHistoricoSimples(item);
        return;
      }

      // Se já tiver conteúdo, pedir confirmação
      cSwal.cConfirm(
        `<div style="text-align: center;">
          <h5>Editar Histórico</h5>
          <p>Deseja editar a descrição deste histórico?</p>
        </div>`,
        (result) => {
          if (result) {
            this.editarHistoricoSimples(item);
          }
        }
      );
    },
    editarHistoricoSimples(item) {
      this.$set(this.editandoHistoricoSimples, item.id, true);
      this.$set(this.textoHistoricoSimples, item.id, item.descricao || '');
    },
    async salvarHistoricoSimples(item) {
      const novaDescricao = this.textoHistoricoSimples[item.id];

      if (!novaDescricao || !novaDescricao.trim()) {
        cSwal.cAlert('Por favor, preencha a descrição do histórico.');
        return;
      }

      this.$set(this.salvandoHistoricoSimples, item.id, true);

      try {
        const historicoData = {
          descricao: novaDescricao.trim()
        };

        await atualizarHistoricoPaciente(item.id, historicoData);

        // Atualizar o item local
        item.descricao = novaDescricao.trim();

        // Limpar estados de edição
        this.$set(this.editandoHistoricoSimples, item.id, false);
        this.$delete(this.textoHistoricoSimples, item.id);

        cSwal.cSuccess('Histórico atualizado com sucesso!');
      } catch (error) {
        console.error('Erro ao salvar histórico simples:', error);
        cSwal.cError('Erro ao salvar o histórico. Tente novamente.');
      } finally {
        this.$set(this.salvandoHistoricoSimples, item.id, false);
      }
    },
    cancelarEdicaoHistoricoSimples(itemId) {
      this.$set(this.editandoHistoricoSimples, itemId, false);
      this.$delete(this.textoHistoricoSimples, itemId);
    },
    selecionarOpcao(tipo) {
      this.dropdownAberto = false;
      if (tipo === 'historico') {
        this.adicionarHistoricoSimples();
      } else if (tipo === 'consulta') {
        this.adicionarConsultaRealizada();
      }
    },
    // Métodos para itens temporários inline
    adicionarHistoricoSimples() {
      const novoItem = {
        tipo: 'historico',
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        descricao: '',
        temporario: true
      };
      this.itensTemporarios.unshift(novoItem);
    },
    adicionarConsultaRealizada() {
      const novoItem = {
        tipo: 'consulta',
        data: moment().format('YYYY-MM-DD'),
        horario: moment().format('HH:mm'),
        categoria: 'acompanhamento',
        valor: '',
        historico_consulta: '',
        proxima_consulta: '',
        temporario: true
      };
      this.itensTemporarios.unshift(novoItem);
    },
    isItemTemporarioValido(item) {
      if (item.tipo === 'consulta') {
        return item.data && item.horario && item.categoria && item.historico_consulta.trim();
      } else {
        return item.data && item.horario && item.descricao.trim();
      }
    },
    async salvarItemTemporario(item, index) {
      try {
        if (item.tipo === 'consulta') {
          await this.salvarConsultaTemporaria(item);
        } else {
          await this.salvarHistoricoTemporario(item);
        }

        // Remover item temporário
        this.itensTemporarios.splice(index, 1);

        // Recarregar dados
        await this.fetchConsultas();

        cSwal.cSuccess(`${item.tipo === 'consulta' ? 'Consulta' : 'Histórico'} salvo com sucesso!`);
      } catch (error) {
        console.error('Erro ao salvar item temporário:', error);
        cSwal.cError('Erro ao salvar. Tente novamente.');
      }
    },
    async salvarConsultaTemporaria(item) {
      // Usar o dentista do token
      const dentistaId = this.$store.state.token?.dentista?.id;

      if (!dentistaId) {
        throw new Error('Dentista não encontrado no token');
      }

      const consultaData = {
        paciente_id: this.paciente.id,
        dentista_id: dentistaId,
        data: item.data,
        horario: item.horario,
        categoria: item.categoria,
        valor: item.valor ? parseFloat(item.valor) : null,
        observacoes: '',
        status: 'realizada',
        confirmada: true
      };

      const consultaResponse = await novaConsulta(consultaData);

      if (consultaResponse) {
        // Criar histórico da consulta
        const modificacoes = [
          {
            titulo: "Histórico da consulta",
            descricao: item.historico_consulta
          },
          {
            titulo: "O que fazer na próxima consulta",
            descricao: item.proxima_consulta || ''
          }
        ];

        const historicoData = {
          paciente_id: this.paciente.id,
          consulta_id: consultaResponse.id,
          data: item.data,
          horario: item.horario + ':00',
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoes)
        };

        await criarHistoricoPaciente(historicoData);
      }
    },
    async salvarHistoricoTemporario(item) {
      const historicoData = {
        paciente_id: this.paciente.id,
        data: item.data,
        horario: item.horario + ':00',
        codigo_acao: 'registro_manual',
        descricao: item.descricao,
        referente_tratamento: false
      };

      await criarHistoricoPaciente(historicoData);
    },
    cancelarItemTemporario(index) {
      this.itensTemporarios.splice(index, 1);
    },

    abrirModalRegistrarHistorico() {
      this.$refs.registrarHistoricoModal.abrirModal(this.paciente.id);
    },
    recarregarDados() {
      this.fetchConsultas();
    }
  }
};
</script>
