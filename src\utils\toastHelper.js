/**
 * Helper para criar toasts elegantes e consistentes em toda a aplicação
 * Baseado no padrão já existente na aplicação
 */

/**
 * Cria um toast com estilo personalizado
 * @param {Object} options - Opções do toast
 * @param {string} options.message - Mensagem do toast
 * @param {string} options.type - Tipo do toast (success, error, info, warning)
 * @param {string} options.title - Tí<PERSON><PERSON> do toast (opcional)
 * @param {number} options.duration - Duração em ms (padrão: 4000)
 * @param {string} options.position - Posição (padrão: 'top-right')
 * @param {boolean} options.showTime - Mostrar horário (padrão: false)
 */
export function showToast(options = {}) {
  const {
    message = '',
    type = 'info',
    title = null,
    duration = 4000,
    position = 'top-right',
    showTime = false
  } = options;

  // Configurações por tipo
  const typeConfig = {
    success: {
      bgClass: 'bg-success',
      icon: 'fas fa-check-circle',
      defaultTitle: 'Sucesso'
    },
    error: {
      bgClass: 'bg-danger',
      icon: 'fas fa-exclamation-circle',
      defaultTitle: 'Erro'
    },
    warning: {
      bgClass: 'bg-warning',
      icon: 'fas fa-exclamation-triangle',
      defaultTitle: 'Atenção'
    },
    info: {
      bgClass: 'bg-info',
      icon: 'fas fa-info-circle',
      defaultTitle: 'Informação'
    }
  };

  const config = typeConfig[type] || typeConfig.info;
  const toastTitle = title || config.defaultTitle;
  const toastId = `toast-${type}-${Date.now()}`;
  
  // Posicionamento
  const positionStyles = {
    'top-right': 'top: 20px; right: 20px;',
    'top-left': 'top: 20px; left: 20px;',
    'bottom-right': 'bottom: 20px; right: 20px;',
    'bottom-left': 'bottom: 20px; left: 20px;',
    'top-center': 'top: 20px; left: 50%; transform: translateX(-50%);',
    'bottom-center': 'bottom: 20px; left: 50%; transform: translateX(-50%);'
  };

  const positionStyle = positionStyles[position] || positionStyles['top-right'];
  const timeString = showTime ? new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }) : '';

  const toastHtml = `
    <div id="${toastId}" class="toast fade show position-fixed lumi-toast" style="${positionStyle} z-index: 9999; min-width: 300px;">
      <div class="toast-header ${config.bgClass} text-white">
        <i class="${config.icon} me-2"></i>
        <strong class="me-auto">${toastTitle}</strong>
        ${timeString ? `<small class="text-white" style="opacity: 0.9;">${timeString}</small>` : ''}
        <button type="button" class="btn-close btn-close-white" onclick="document.getElementById('${toastId}').remove()"></button>
      </div>
      <div class="toast-body" style="background: white; color: #495057;">
        ${message}
      </div>
    </div>
  `;

  // Adicionar ao DOM
  document.body.insertAdjacentHTML('beforeend', toastHtml);

  // Animar entrada
  const toastElement = document.getElementById(toastId);
  if (toastElement) {
    // Adicionar classe de animação
    toastElement.classList.add('toast-slide-in');
    
    // Remover automaticamente após duração especificada
    setTimeout(() => {
      if (toastElement && toastElement.parentNode) {
        toastElement.classList.remove('show');
        toastElement.classList.add('toast-slide-out');
        setTimeout(() => {
          if (toastElement && toastElement.parentNode) {
            toastElement.remove();
          }
        }, 300);
      }
    }, duration);
  }

  return toastId;
}

/**
 * Toast de sucesso para auto-save
 * @param {string} message - Mensagem personalizada (opcional)
 */
export function showAutoSaveToast(message = 'Alterações salvas automaticamente') {
  return showToast({
    message: `<i class="fas fa-save me-2 text-success"></i>${message}`,
    type: 'success',
    title: 'Alterações salvas',
    duration: 3000,
    showTime: true
  });
}

/**
 * Toast de informação
 * @param {string} message - Mensagem
 * @param {Object} options - Opções adicionais
 */
export function showInfoToast(message, options = {}) {
  return showToast({
    message,
    type: 'info',
    ...options
  });
}

/**
 * Toast de sucesso
 * @param {string} message - Mensagem
 * @param {Object} options - Opções adicionais
 */
export function showSuccessToast(message, options = {}) {
  return showToast({
    message,
    type: 'success',
    ...options
  });
}

/**
 * Toast de erro
 * @param {string} message - Mensagem
 * @param {Object} options - Opções adicionais
 */
export function showErrorToast(message, options = {}) {
  return showToast({
    message,
    type: 'error',
    ...options
  });
}

/**
 * Toast de aviso
 * @param {string} message - Mensagem
 * @param {Object} options - Opções adicionais
 */
export function showWarningToast(message, options = {}) {
  return showToast({
    message,
    type: 'warning',
    ...options
  });
}

/**
 * Remove todos os toasts ativos
 */
export function clearAllToasts() {
  const toasts = document.querySelectorAll('.lumi-toast');
  toasts.forEach(toast => {
    toast.classList.remove('show');
    toast.classList.add('toast-slide-out');
    setTimeout(() => {
      if (toast && toast.parentNode) {
        toast.remove();
      }
    }, 300);
  });
}

export default {
  showToast,
  showAutoSaveToast,
  showInfoToast,
  showSuccessToast,
  showErrorToast,
  showWarningToast,
  clearAllToasts
};
