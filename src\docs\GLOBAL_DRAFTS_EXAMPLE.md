# Exemplo Visual do Sistema Global de Drafts

## Cenário de Uso

Imagine que você é um dentista usando o sistema e está trabalhando com múltiplos pacientes:

### Situação Inicial
```
👨‍⚕️ Dr. Silva está editando o perfil de <PERSON>
📝 Faz algumas alterações nos dados pessoais
🔄 Navega para ver outro paciente (<PERSON>)
```

### O que acontece:
1. **Sistema detecta navegação com alterações pendentes**
2. **Salva automaticamente no localStorage**
3. **Adiciona FAB global para João Santos**
4. **Permite navegação livre**

---

## Estados Visuais

### 1. Sem Drafts
```
┌─────────────────────────────────────┐
│                                     │
│         Página do Sistema           │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
└─────────────────────────────────────┘
```

### 2. Um Draft Ativo
```
┌─────────────────────────────────────┐
│                                     │
│         Página do Sistema           │
│                                     │
│                                     │
│                                     │
│                                     │
│                              ┌─────┐│
│                              │ 📝3 ││
│                              │João ││
│                              │Santos│
│                              └─────┘│
└─────────────────────────────────────┘
```

### 3. <PERSON>úl<PERSON>los Drafts Empilhados
```
┌─────────────────────────────────────┐
│                                     │
│         Página do Sistema           │
│                                     │
│                                     │
│                              ┌─────┐│
│                              │ 📝2 ││
│                              │Ana  ││
│                              │Costa││
│                              └─────┘│
│                              ┌─────┐│
│                              │ 📝5 ││
│                              │Maria││
│                              │Silva││
│                              └─────┘│
│                              ┌─────┐│
│                              │ 📝3 ││
│                              │João ││
│                              │Santos│
│                              └─────┘│
└─────────────────────────────────────┘
```

---

## Interação com Tooltip

### Clique no FAB
```
┌─────────────────────────────────────┐
│                                     │
│         Página do Sistema           │
│                                     │
│    ┌─────────────────────────────┐  │
│    │ 👤 João Santos              │  │
│    │ ⏰ Última alteração:        │  │
│    │    03/08/2025 14:30         │  │
│    │ 📝 3 campos alterados       │  │
│    │                             │  │
│    │ [👁️ Verificar e salvar]     │  │
│    │ [👤 Abrir paciente]         │  │
│    └─────────────────────────────┘  │
│                              ┌─────┐│
│                              │ 📝3 ││
│                              │João ││
│                              │Santos│
│                              └─────┘│
└─────────────────────────────────────┘
```

---

## Modal de Revisão

### Clique em "Verificar e salvar"
```
┌─────────────────────────────────────────────────────────────┐
│ 👁️ Revisar alterações - João Santos                    [✕] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ℹ️ Encontramos 3 alterações não salvas.                    │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Campo              │ Valor Anterior │ Novo Valor        │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Dados Pessoais (2 alterações)                          │ │
│ │ Nome completo      │ João Silva     │ João Santos       │ │
│ │ Telefone           │ (11) 9999-9999 │ (11) 8888-8888    │ │
│ │ Endereço (1 alteração)                                 │ │
│ │ CEP                │ 01234-567      │ 12345-678         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ⏰ Última modificação: 03/08/2025 14:30                    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                           [Cancelar] [💾 Salvar alterações] │
└─────────────────────────────────────────────────────────────┘
```

---

## Fluxo Completo de Uso

### Passo 1: Edição
```
👨‍⚕️ Usuário edita João Santos
📝 Altera nome, telefone e CEP
🔄 Clica para ir para Maria Oliveira
```

### Passo 2: Auto-save
```
💾 Sistema salva automaticamente no localStorage
🏷️ Cria draft: "João Santos - 3 alterações"
➕ Adiciona FAB global
✅ Permite navegação
```

### Passo 3: Trabalho Contínuo
```
👨‍⚕️ Usuário edita Maria Oliveira
📝 Altera endereço e telefone
🔄 Vai para Ana Costa
💾 Sistema cria segundo draft
📚 Agora há 2 FABs empilhados
```

### Passo 4: Resolução
```
👆 Usuário clica no FAB de João Santos
👁️ Escolhe "Verificar e salvar"
📋 Vê preview das alterações
💾 Confirma salvamento
✅ Draft removido, FAB desaparece
```

---

## Vantagens Demonstradas

### ✅ Não Intrusivo
- Usuário pode navegar livremente
- Não há bloqueios ou pop-ups forçados
- Trabalho flui naturalmente

### ✅ Visualmente Claro
- FABs mostram exatamente o que há pendente
- Badge com número de alterações
- Nome do paciente sempre visível

### ✅ Funcional
- Gerencia múltiplos pacientes simultaneamente
- Permite revisão antes do salvamento
- Navegação rápida entre pacientes

### ✅ Seguro
- Nenhuma alteração é perdida
- Dados persistem entre sessões
- Confirmação antes de salvar no banco

---

## Casos de Uso Avançados

### Cenário 1: Sessão Interrompida
```
1. Usuário edita 3 pacientes
2. Fecha o navegador sem salvar
3. Reabre o sistema no dia seguinte
4. FABs aparecem automaticamente
5. Pode continuar de onde parou
```

### Cenário 2: Múltiplas Abas
```
1. Usuário abre paciente em nova aba
2. Faz alterações
3. Volta para aba original
4. FAB aparece também na aba original
5. Sistema sincronizado entre abas
```

### Cenário 3: Revisão em Lote
```
1. Usuário acumula 5 drafts durante o dia
2. No final do expediente
3. Revisa e salva todos de uma vez
4. Sistema limpa automaticamente
5. Próximo dia começa limpo
```

---

## Feedback Visual

### Estados do FAB
- **Normal**: Vermelho com badge amarelo
- **Hover**: Cresce e move para esquerda
- **Empilhado**: Menor e mais transparente
- **Ativo**: Tooltip aberto

### Animações
- **Entrada**: Slide suave da direita
- **Hover**: Escala e sombra
- **Empilhamento**: Efeito de profundidade
- **Saída**: Fade out suave

### Cores e Significados
- 🔴 **Vermelho**: Alterações pendentes
- 🟡 **Amarelo**: Número de alterações
- 🔵 **Azul**: Ação de abrir paciente
- 🟢 **Verde**: Ação de salvar

---

## Responsividade

### Desktop (> 768px)
- FABs largos (280px)
- Tooltips espaçosos (320px)
- Botões lado a lado

### Tablet (768px - 480px)
- FABs médios (260px)
- Tooltips ajustados (280px)
- Layout otimizado

### Mobile (< 480px)
- FABs compactos
- Tooltips em tela cheia
- Botões empilhados
- Touch-friendly
