<template>
  <div class="fatura-form-elegant">
    <div class="row g-4">
      <!-- <PERSON>una Esquerda -->
      <div class="col-md-6">
        <!-- Informações Básicas -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-user-circle me-2"></i>
            <span>Informações Básicas</span>
          </div>

          <div class="row g-3">
            <!-- Paciente -->
            <div class="col-12">
              <label class="form-label elegant-label">Paciente *</label>
              <paciente-busca-button
                :paciente-selecionado="pacienteSelecionado"
                :has-error="!!errors.paciente_id"
                :error-message="errors.paciente_id"
                :disabled="preselectedPaciente"
                placeholder="Clique na lupa para buscar um paciente..."
                @abrir-busca="$emit('abrir-busca-paciente')"
                @limpar-selecao="$emit('limpar-paciente')"
              />
            </div>

            <!-- Descrição -->
            <div class="col-12">
              <label class="form-label elegant-label">Descrição *</label>
              <input type="text"
                     class="form-control elegant-input"
                     :value="form.descricao"
                     @input="$emit('update-form', 'descricao', $event.target.value)"
                     :class="{ 'is-invalid': errors.descricao }"
                     placeholder="Descrição da fatura">
              <div class="invalid-feedback" v-if="errors.descricao">
                {{ errors.descricao }}
              </div>
            </div>

            <!-- Dentista -->
            <div class="col-12">
              <label class="form-label elegant-label">Ortodontista</label>
              <select class="form-select elegant-select"
                      :value="form.dentista_id"
                      @change="$emit('update-form', 'dentista_id', $event.target.value)">
                <option value="">Selecione um ortodontista</option>
                <option v-for="dentista in dentistas" :key="dentista?.id || Math.random()" :value="dentista?.id">
                  {{ dentista?.nome || 'Ortodontista sem nome' }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Observações -->
        <div class="form-section mt-3">
          <div class="section-header">
            <i class="fas fa-sticky-note me-2"></i>
            <span>Observações</span>
          </div>

          <textarea class="form-control elegant-textarea"
                    rows="4"
                    :value="form.observacoes"
                    @input="$emit('update-form', 'observacoes', $event.target.value)"
                    placeholder="Observações adicionais sobre a fatura..."></textarea>
        </div>
      </div>

      <!-- Coluna Direita -->
      <div class="col-md-6">
        <!-- Valores, Vencimento e Ajustes -->
        <div class="form-section">
          <div class="section-header">
            <i class="fas fa-calculator me-2"></i>
            <span>Valores e Ajustes</span>
          </div>

          <div class="row g-3">
            <!-- Valor Nominal -->
            <div class="col-md-6">
              <label class="form-label elegant-label">Valor Nominal *</label>
              <div class="elegant-input-group large-input-group" :class="{ 'is-invalid': errors.valor_nominal }">
                <span class="elegant-addon elegant-addon-left">R$</span>
                <input type="text"
                       class="elegant-input money-input text-center is-invalid"
                       :value="formatCurrencyInput(form.valor_nominal)"
                       @input="updateValorNominal($event.target.value, $event)"
                       placeholder="0,00"
                       maxlength="15">
              </div>
              <div class="invalid-feedback" v-if="errors.valor_nominal">
                {{ errors.valor_nominal }}
              </div>
            </div>

            <!-- Data de Vencimento -->
            <div class="col-md-6">
              <label class="form-label elegant-label">Vencimento *</label>
              <input type="date"
                     class="form-control elegant-input large-input text-center is-invalid"
                     :value="form.data_vencimento"
                     @change="$emit('update-form', 'data_vencimento', $event.target.value)"
                     :class="{ 'is-invalid': errors.data_vencimento }">
              <div class="invalid-feedback" v-if="errors.data_vencimento">
                {{ errors.data_vencimento }}
              </div>
            </div>

            <!-- Parcelas -->
            <div class="col-md-5">
              <label class="form-label elegant-label text-center">Parcelas</label>
              <div class="elegant-input-group" :class="{ 'is-invalid': errors.parcelas_total }">
                <input type="number"
                       min="1"
                       max="60"
                       class="elegant-input text-center"
                       :value="form.parcelas_total"
                       @input="$emit('update-form', 'parcelas_total', parseInt($event.target.value) || 1)">
                <span class="elegant-addon elegant-addon-right">X</span>
              </div>
              <div class="invalid-feedback" v-if="errors.parcelas_total">
                {{ errors.parcelas_total }}
              </div>
            </div>

            <!-- Valor por Parcela -->
            <div class="col-md-7">
              <label class="form-label elegant-label">Valor/Parcela</label>
              <div class="elegant-input-group">
                <span class="elegant-addon elegant-addon-left">R$</span>
                <input type="text"
                       class="elegant-input text-center"
                       :value="formatCurrency(valorPorParcela)"
                       readonly>
              </div>
            </div>

            <!-- Descontos -->
            <div class="col-md-5">
              <label class="form-label elegant-label text-center">Desconto %</label>
              <div class="elegant-input-group" :class="{ 'field-active-group': activeDescontoField === 'percent' }">
                <input type="number"
                       min="0"
                       max="100"
                       step="0.01"
                       class="elegant-input percent-input text-center"
                       :value="form.percentual_desconto"
                       @input="updatePercentualDesconto($event.target.value)"
                       @focus="setActiveDescontoField('percent')"
                       placeholder="0">
                <span class="elegant-addon elegant-addon-right">%</span>
              </div>
            </div>
            <div class="col-md-7">
              <label class="form-label elegant-label">Desconto R$</label>
              <div class="elegant-input-group" :class="{ 'field-active-group': activeDescontoField === 'value' }">
                <span class="elegant-addon elegant-addon-right">R$</span>
                <input type="text"
                       class="elegant-input money-input text-center"
                       :value="formatCurrencyInput(form.valor_desconto)"
                       @input="updateValorDesconto($event.target.value, $event)"
                       @focus="setActiveDescontoField('value')"
                       placeholder="0,00"
                       maxlength="15">
              </div>
            </div>

            <!-- Acréscimos -->
            <div class="col-md-5">
              <label class="form-label elegant-label text-center">Acréscimo %</label>
              <div class="elegant-input-group" :class="{ 'field-active-group': activeAcrescimoField === 'percent' }">
                <input type="number"
                       min="0"
                       max="100"
                       step="0.01"
                       class="elegant-input percent-input text-center"
                       :value="form.percentual_acrescimo"
                       @input="updatePercentualAcrescimo($event.target.value)"
                       @focus="setActiveAcrescimoField('percent')"
                       placeholder="0">
                <span class="elegant-addon elegant-addon-right">%</span>
              </div>
            </div>
            <div class="col-md-7">
              <label class="form-label elegant-label">Acréscimo R$</label>
              <div class="elegant-input-group" :class="{ 'field-active-group': activeAcrescimoField === 'value' }">
                <span class="elegant-addon elegant-addon-left">R$</span>
                <input type="text"
                       class="elegant-input money-input text-center"
                       :value="formatCurrencyInput(form.valor_acrescimo)"
                       @input="updateValorAcrescimo($event.target.value, $event)"
                       @focus="setActiveAcrescimoField('value')"
                       placeholder="0,00"
                       maxlength="15">
              </div>
            </div>
          </div>
        </div>

        <!-- Cards de Valores Finais -->
        <div class="valores-finais-section">
          <div class="row g-3">
            <!-- Valor Final -->
            <div class="col-8">
              <div class="valor-final-card">
                <div class="valor-final-label">Valor Final</div>
                <div class="valor-final-value">{{ formatCurrency(calculatedFinalValue) }}</div>
              </div>
            </div>

            <!-- Valor por Parcela -->
            <div class="col-4">
              <div class="parcela-card">
                <div class="parcela-value">{{ formatCurrency(valorPorParcela) }}</div>
                <div class="parcela-label">{{ form.parcelas_total }}x parcela{{ form.parcelas_total > 1 ? 's' : '' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import PacienteBuscaButton from '@/components/Global/PacienteBuscaButton.vue';

export default {
  name: 'FaturaFormElegant',
  components: {
    PacienteBuscaButton,
  },
  data() {
    return {
      // Controla qual campo está ativo para destacar visualmente
      activeDescontoField: 'none', // 'percent', 'value', 'none'
      activeAcrescimoField: 'none'  // 'percent', 'value', 'none'
    };
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    errors: {
      type: Object,
      default: () => ({})
    },
    pacientes: {
      type: Array,
      default: () => []
    },
    dentistas: {
      type: Array,
      default: () => []
    },
    preselectedPaciente: {
      type: [String, Number],
      default: null
    },
    pacienteSelecionado: {
      type: Object,
      default: null
    }
  },
  computed: {
    calculatedFinalValue() {
      return financeiroService.calculateFinalValue(
        this.form.valor_nominal,
        this.form.percentual_desconto,
        this.form.valor_desconto,
        this.form.percentual_acrescimo,
        this.form.valor_acrescimo
      );
    },

    valorPorParcela() {
      const parcelas = this.form.parcelas_total || 1;
      return this.calculatedFinalValue / parcelas;
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,

    // Formatação de entrada para valores monetários
    formatCurrencyInput(value) {
      if (!value || value === 0) return '';
      return this.formatCurrency(value).replace('R$ ', '');
    },

    // Formatação de entrada para percentuais
    formatPercentInput(value) {
      if (!value || value === 0) return '';
      return value.toString().replace('.', ',');
    },

    // Máscara de dinheiro - converte centavos para reais
    formatMoneyMask(value) {
      // Remove tudo que não é dígito
      const digits = value.replace(/\D/g, '');

      if (!digits) return '';

      // Converte para centavos (últimos 2 dígitos são centavos)
      const cents = parseInt(digits);
      const reais = cents / 100;

      // Formata como moeda brasileira
      return reais.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // Converte string monetária formatada para número
    parseCurrencyInput(value) {
      if (!value) return 0;
      // Remove formatação e converte para número
      const cleanValue = value.replace(/[^\d,]/g, '').replace(',', '.');
      return parseFloat(cleanValue) || 0;
    },

    // Converte string percentual para número
    parsePercentInput(value) {
      if (!value) return 0;
      return parseFloat(value.replace(',', '.')) || 0;
    },

    updateValorNominal(value, event) {
      // Aplica máscara e atualiza o campo visual
      const maskedValue = this.formatMoneyMask(value);

      // Atualiza o campo visual
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }

      // Converte para número e emite
      const numValue = this.parseCurrencyInput(maskedValue);
      this.$emit('update-form', 'valor_nominal', numValue);
    },

    updatePercentualDesconto(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'percentual_desconto', numValue);

      // Calcular valor baseado na porcentagem
      if (numValue > 0) {
        const valorNominal = this.form.valor_nominal || 0;
        const valorDesconto = (valorNominal * numValue) / 100;
        this.$emit('update-form', 'valor_desconto', valorDesconto);
        this.activeDescontoField = 'percent';
      } else {
        this.$emit('update-form', 'valor_desconto', 0);
        this.activeDescontoField = 'none';
      }
    },

    updateValorDesconto(value, event) {
      // Aplica máscara e atualiza o campo visual
      const maskedValue = this.formatMoneyMask(value);

      // Atualiza o campo visual
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }

      // Converte para número e emite
      const numValue = this.parseCurrencyInput(maskedValue);
      this.$emit('update-form', 'valor_desconto', numValue);

      // Calcular porcentagem baseada no valor
      if (numValue > 0) {
        const valorNominal = this.form.valor_nominal || 0;
        const percentualDesconto = valorNominal > 0 ? (numValue * 100) / valorNominal : 0;
        this.$emit('update-form', 'percentual_desconto', percentualDesconto);
        this.activeDescontoField = 'value';
      } else {
        this.$emit('update-form', 'percentual_desconto', 0);
        this.activeDescontoField = 'none';
      }
    },

    updatePercentualAcrescimo(value) {
      const numValue = parseFloat(value) || 0;
      this.$emit('update-form', 'percentual_acrescimo', numValue);

      // Calcular valor baseado na porcentagem
      if (numValue > 0) {
        const valorNominal = this.form.valor_nominal || 0;
        const valorAcrescimo = (valorNominal * numValue) / 100;
        this.$emit('update-form', 'valor_acrescimo', valorAcrescimo);
        this.activeAcrescimoField = 'percent';
      } else {
        this.$emit('update-form', 'valor_acrescimo', 0);
        this.activeAcrescimoField = 'none';
      }
    },

    updateValorAcrescimo(value, event) {
      // Aplica máscara e atualiza o campo visual
      const maskedValue = this.formatMoneyMask(value);

      // Atualiza o campo visual
      if (event && event.target) {
        this.$nextTick(() => {
          event.target.value = maskedValue;
        });
      }

      // Converte para número e emite
      const numValue = this.parseCurrencyInput(maskedValue);
      this.$emit('update-form', 'valor_acrescimo', numValue);

      // Calcular porcentagem baseada no valor
      if (numValue > 0) {
        const valorNominal = this.form.valor_nominal || 0;
        const percentualAcrescimo = valorNominal > 0 ? (numValue * 100) / valorNominal : 0;
        this.$emit('update-form', 'percentual_acrescimo', percentualAcrescimo);
        this.activeAcrescimoField = 'value';
      } else {
        this.$emit('update-form', 'percentual_acrescimo', 0);
        this.activeAcrescimoField = 'none';
      }
    },

    // Métodos para controlar qual campo está ativo
    setActiveDescontoField(field) {
      this.activeDescontoField = field;
    },

    setActiveAcrescimoField(field) {
      this.activeAcrescimoField = field;
    }
  }
};
</script>

<style scoped>
/* Container Principal */
.fatura-form-elegant {
  padding: 0;
}

/* Seções do Formulário */
.form-section {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  height: fit-content;
}

.form-section:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
  font-size: 0.95rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f3f4;
}

.section-header i {
  color: #2C82C9;
  font-size: 1.1rem;
}

/* Labels Elegantes */
.elegant-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.4rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Inputs Elegantes */
.elegant-input {
  border: 1px solid #dee2e6;
  /* border-radius: 8px; */
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  min-height: 38px;
  transition: all 0.3s ease;
  /* background: #fafbfc; */
}

.elegant-input:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
  background: white;
  outline: none;
}

.elegant-select {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  line-height: 1.5;
  min-height: 38px;
  transition: all 0.3s ease;
  background: #fafbfc;
  display: flex;
  align-items: center;
}

.elegant-select:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
  background: white;
  outline: none;
}

/* Input Groups Elegantes - Versão Robusta */
.elegant-input-group {
  position: relative;
  display: flex;
  width: 100%;
  min-height: 38px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  /* background: white; */
  border: 1px solid #dee2e6;
  overflow: hidden;
  transition: all 0.3s ease;
}

.elegant-input-group:focus-within {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
}

.elegant-input-group .elegant-input {
  border: none;
  background: transparent;
  flex: 1;
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  min-height: 36px;
  outline: none;
  transition: all 0.3s ease;
}

.elegant-input-group .elegant-input:focus {
  outline: none;
  background: rgba(44, 130, 201, 0.02);
}

.elegant-addon {
  background: linear-gradient(135deg, #7bb3e0 0%, #6ba3d0 100%);
  color: white;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.5rem 0.6rem;
  width: 50px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  flex-shrink: 0;
  border: none;
}

/* Variações para posicionamento */
.elegant-addon-left {
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.elegant-addon-right {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

/* Addon especial para ID da ficha do paciente */
.elegant-addon-ficha {
  background: linear-gradient(135deg, #5a9bd4 0%, #4a8bc2 100%) !important;
}

/* Correção para addons à direita com inputs centralizados */
.elegant-input-group .elegant-input.text-center + .elegant-addon-right {
  order: 2;
  align-self: stretch;
}

.elegant-input-group .elegant-input.text-center {
  order: 1;
  flex: 1;
}

/* Campos maiores para Valor Nominal e Vencimento */
.large-input {
  min-height: 48px !important;
  padding: 0.7rem 0.8rem;
  font-size: 1rem;
}

.large-input-group {
  min-height: 48px;
}

.large-input-group .elegant-input {
  min-height: 48px;
  padding: 0.7rem 0.8rem;
  font-size: 1rem;
}

.large-input-group .elegant-addon {
  min-height: 48px;
  padding: 0.7rem 0.8rem;
  font-size: 0.9rem;
}

/* Textarea Elegante */
.elegant-textarea {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.5rem 0.6rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #fafbfc;
  resize: vertical;
  min-height: 80px;
}

.elegant-textarea:focus {
  border-color: #2C82C9;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.15);
  background: white;
  outline: none;
}

/* Seção de Valores Finais */
.valores-finais-section {
  margin-top: 1.5rem;
}

/* Valor Final */
.valor-final-card {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.25);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.valor-final-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.35);
}

.valor-final-label {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
  margin-bottom: 0.3rem;
}

.valor-final-value {
  font-size: 1.4rem;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Card de Parcela */
.parcela-card {
  background: linear-gradient(135deg, #2C82C9 0%, #1a73e8 100%);
  color: white;
  padding: 1.2rem 0.8rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(44, 130, 201, 0.25);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.parcela-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(44, 130, 201, 0.35);
}

.parcela-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.parcela-label {
  font-size: 0.65rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  opacity: 0.9;
}

/* Estados de Erro */
.elegant-input-group.is-invalid, .elegant-input.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.15) !important;
}

.invalid-feedback {
  text-align: center;
  display: block;
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.3rem;
  font-weight: 500;
}

/* Grupo Ativo - Destaque Visual (sem mudança de tamanho) */
.field-active-group {
  border-color: #2C82C9 !important;
  box-shadow: 0 0 0 0.15rem rgba(44, 130, 201, 0.2) !important;
}

.field-active-group .elegant-input {
  background: rgba(44, 130, 201, 0.03) !important;
}

.field-active-group .elegant-addon {
  background: linear-gradient(135deg, #6ba3d0 0%, #7bb3e0 100%);
  box-shadow: 0 2px 8px rgba(107, 163, 208, 0.3);
}

/* Inputs especiais */
.money-input::-webkit-outer-spin-button,
.money-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.money-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.percent-input::-webkit-outer-spin-button,
.percent-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.percent-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Responsividade */
@media (max-width: 768px) {
  .form-section {
    padding: 1rem;
  }

  .section-header {
    font-size: 0.9rem;
  }

  .valor-final-value {
    font-size: 1.2rem;
  }

  .parcela-value {
    font-size: 1rem;
  }

  .elegant-input,
  .elegant-select,
  .elegant-textarea {
    padding: 0.5rem 0.7rem;
    font-size: 0.85rem;
  }

  .elegant-addon {
    padding: 0.5rem 0.6rem;
    font-size: 0.8rem;
    min-width: 40px;
  }

  .valores-finais-section .col-8,
  .valores-finais-section .col-4 {
    margin-bottom: 0.5rem;
  }
}

/* Animações suaves */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-section {
  animation: fadeInUp 0.4s ease-out;
}

.valores-finais-section {
  animation: fadeInUp 0.6s ease-out;
}
</style>
