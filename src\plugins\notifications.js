import notificationsService from '@/services/notificationsService'
import { computed } from 'vue'

// Importação dinâmica da store para evitar importação circular
let store = null
const getStore = () => {
  if (!store) {
    store = require('@/store').default
  }
  return store
}

class NotificationsPlugin {
  constructor() {
    this.isInitialized = false
    this.visibilityChangeHandler = null
    this.beforeUnloadHandler = null
  }

  install(app) {
    // Adicionar o serviço como propriedade global
    app.config.globalProperties.$notifications = notificationsService

    // Não usar mixin global - será inicializado manualmente no componente principal
  }

  async initializeNotifications() {
    if (this.isInitialized) return

    try {
      console.log('Inicializando sistema de notificações...')
      
      // Inicializar o serviço
      await notificationsService.init()
      
      // Configurar listeners para otimizar performance
      this.setupVisibilityHandlers()
      this.setupBeforeUnloadHandler()
      
      this.isInitialized = true
      console.log('Sistema de notificações inicializado com sucesso')
      
    } catch (error) {
      console.error('Erro ao inicializar sistema de notificações:', error)
    }
  }

  setupVisibilityHandlers() {
    // Pausar/retomar polling baseado na visibilidade da página
    this.visibilityChangeHandler = () => {
      if (document.hidden) {
        // Página não está visível, reduzir frequência do polling
        notificationsService.stopPolling()
        console.log('Página oculta - polling pausado')
      } else {
        // Página está visível, retomar polling normal
        notificationsService.startPolling(30000) // 30 segundos
        // Buscar atualizações imediatamente quando a página voltar a ficar visível
        // Usar modo silencioso para não quebrar se houver erro
        notificationsService.fetchUnreadCount({ silent: true })
        console.log('Página visível - polling retomado')
      }
    }

    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
  }

  setupBeforeUnloadHandler() {
    // Limpar recursos antes de sair da página
    this.beforeUnloadHandler = () => {
      notificationsService.destroy()
    }

    window.addEventListener('beforeunload', this.beforeUnloadHandler)
  }

  destroy() {
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
    }
    
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler)
    }
    
    notificationsService.destroy()
    this.isInitialized = false
  }
}

// Criar instância singleton
const notificationsPlugin = new NotificationsPlugin()

// Mixin global para facilitar o uso
export const NotificationsMixin = {
  computed: {
    $notificationsService() {
      return notificationsService
    },
    
    $hasUnreadNotifications() {
      return this.$store.getters['notifications/hasUnreadNotifications']
    },
    
    $unreadNotificationsCount() {
      return this.$store.getters['notifications/unreadCount']
    }
  },
  
  methods: {
    async $fetchNotifications(options = {}) {
      return await notificationsService.fetchNotifications(options)
    },

    async $refreshNotifications(options = {}) {
      return await notificationsService.refreshNotifications(options)
    },
    
    async $markNotificationAsRead(notificationId) {
      return await notificationsService.markAsRead(notificationId)
    },
    
    async $markAllNotificationsAsRead() {
      return await notificationsService.markAllAsRead()
    },
    
    async $deleteNotification(notificationId) {
      return await notificationsService.deleteNotification(notificationId)
    },
    
    async $createNotification(notificationData) {
      return await notificationsService.createNotification(notificationData)
    },
    
    $formatNotificationDate(dateString) {
      return notificationsService.formatNotificationDate(dateString)
    },
    
    $getNotificationIcon(type) {
      return notificationsService.getNotificationIcon(type)
    },
    
    $getNotificationColor(type) {
      return notificationsService.getNotificationColor(type)
    }
  }
}

// Composable para Vue 3 Composition API (se necessário)
export function useNotifications() {
  return {
    notificationsService,
    
    // Reactive getters
    hasUnreadNotifications: computed(() =>
      getStore().getters['notifications/hasUnreadNotifications']
    ),

    unreadCount: computed(() =>
      getStore().getters['notifications/unreadCount']
    ),

    allNotifications: computed(() =>
      getStore().getters['notifications/allNotifications']
    ),

    isLoading: computed(() =>
      getStore().getters['notifications/isLoading']
    ),

    error: computed(() =>
      getStore().getters['notifications/error']
    ),
    
    // Methods
    async fetchNotifications(options = {}) {
      return await notificationsService.fetchNotifications(options)
    },
    
    async markAsRead(notificationId) {
      return await notificationsService.markAsRead(notificationId)
    },
    
    async markAllAsRead() {
      return await notificationsService.markAllAsRead()
    },
    
    async deleteNotification(notificationId) {
      return await notificationsService.deleteNotification(notificationId)
    },
    
    formatDate(dateString) {
      return notificationsService.formatNotificationDate(dateString)
    },
    
    getIcon(type) {
      return notificationsService.getNotificationIcon(type)
    },
    
    getColor(type) {
      return notificationsService.getNotificationColor(type)
    }
  }
}

export default notificationsPlugin
